package co.com.gedsys.base.application.usecase.planeacion.consecutivos;

import co.com.gedsys.base.application.dto.ConsecutivoDTO;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoWithoutTipoDocumentalException;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CrearConsecutivoUseCaseTest {

    @Mock
    private ConsecutivoRepository consecutivoRepository;

    @Mock
    private TipoDocumentalRepository tipoDocumentalRepository;

    @Mock
    private ClasificacionDocumentalRepository clasificacionDocumentalRepository;

    @Mock
    private ConsecutivoMapper mapper;

    @InjectMocks
    private CrearConsecutivoUseCase useCase;

    @Captor
    private ArgumentCaptor<Consecutivo> consecutivoCaptor;

    private UUID tipoDocumentalId;
    private UUID clasificacionDocumentalId;
    private TipoDocumental tipoDocumental;
    private ClasificacionDocumental clasificacionDocumental;
    private ConsecutivoDTO consecutivoDTO;

    @BeforeEach
    void setUp() {
        tipoDocumentalId = UUID.randomUUID();
        clasificacionDocumentalId = UUID.randomUUID();

        tipoDocumental = new TipoDocumental("Tipo Documental Test");
        tipoDocumental.setId(tipoDocumentalId);

        Seccion seccion = mock(Seccion.class);
        SerieDocumental serieDocumental = mock(SerieDocumental.class);
        clasificacionDocumental = new ClasificacionDocumental(seccion, serieDocumental);
        clasificacionDocumental.setId(clasificacionDocumentalId);

        consecutivoDTO = new ConsecutivoDTO(
            UUID.randomUUID().toString(),
            "RECEPCION",
            "ACTIVO",
            "PRE",
            "SUF",
            1,
            null,
            null
        );
    }

    @Test
    @DisplayName("Debe crear un consecutivo regular correctamente")
    void debeCrearConsecutivoRegularCorrectamente() {
        // Arrange
        CrearConsecutivoCommand command = new CrearConsecutivoCommand(
                TipoConsecutivo.RECEPCION,
                "PRE",
                "SUF",
                1,
                null,
                null
        );

        Consecutivo consecutivoGuardado = new Consecutivo("PRE", "SUF", 1, TipoConsecutivo.RECEPCION);
        consecutivoGuardado.setId(UUID.randomUUID());

        when(consecutivoRepository.findByExample(any(Consecutivo.class))).thenReturn(Optional.empty());
        when(consecutivoRepository.save(any(Consecutivo.class))).thenReturn(consecutivoGuardado);
        when(mapper.toDto(any(Consecutivo.class))).thenReturn(consecutivoDTO);

        // Act
        ConsecutivoDTO resultado = useCase.execute(command);

        // Assert
        verify(consecutivoRepository, times(2)).findByExample(consecutivoCaptor.capture());
        Consecutivo consecutivoParaValidar = consecutivoCaptor.getValue();

        assertEquals(TipoConsecutivo.RECEPCION, consecutivoParaValidar.getTipoConsecutivo());
        assertNull(consecutivoParaValidar.getTipoDocumental());
        assertNull(consecutivoParaValidar.getClasificacionDocumental());

        verify(consecutivoRepository).save(any(Consecutivo.class));
        verify(mapper).toDto(any(Consecutivo.class));

        assertNotNull(resultado);
        assertEquals("PRE", resultado.prefijo());
        assertEquals("SUF", resultado.sufijo());
        assertEquals(1, resultado.contador());
    }

    @Test
    @DisplayName("Debe crear un consecutivo de producción correctamente")
    void debeCrearConsecutivoProduccionCorrectamente() {
        // Arrange
        CrearConsecutivoCommand command = new CrearConsecutivoCommand(
                TipoConsecutivo.PRODUCCION,
                "PRE",
                "SUF",
                1,
                tipoDocumentalId,
                clasificacionDocumentalId
        );

        Consecutivo consecutivoGuardado = new Consecutivo("PRE", "SUF", 1, tipoDocumental, clasificacionDocumental);
        consecutivoGuardado.setId(UUID.randomUUID());

        when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(tipoDocumental));
        when(clasificacionDocumentalRepository.findById(clasificacionDocumentalId)).thenReturn(Optional.of(clasificacionDocumental));
        when(consecutivoRepository.findByExample(any(Consecutivo.class))).thenReturn(Optional.empty());
        when(consecutivoRepository.save(any(Consecutivo.class))).thenReturn(consecutivoGuardado);
        when(mapper.toDto(any(Consecutivo.class))).thenReturn(consecutivoDTO);

        // Act
        ConsecutivoDTO resultado = useCase.execute(command);

        // Assert
        verify(tipoDocumentalRepository).findById(tipoDocumentalId);
        verify(clasificacionDocumentalRepository).findById(clasificacionDocumentalId);
        verify(consecutivoRepository).findByExample(consecutivoCaptor.capture());

        Consecutivo consecutivoParaValidar = consecutivoCaptor.getValue();
        assertEquals(TipoConsecutivo.PRODUCCION, consecutivoParaValidar.getTipoConsecutivo());
        assertEquals(tipoDocumental, consecutivoParaValidar.getTipoDocumental());
        assertEquals(clasificacionDocumental, consecutivoParaValidar.getClasificacionDocumental());

        verify(consecutivoRepository).save(any(Consecutivo.class));
        verify(mapper).toDto(any(Consecutivo.class));

        assertNotNull(resultado);
    }

    @Test
    @DisplayName("Debe lanzar excepción cuando el consecutivo ya existe")
    void debeLanzarExcepcionCuandoConsecutivoYaExiste() {
        // Arrange
        CrearConsecutivoCommand command = new CrearConsecutivoCommand(
                TipoConsecutivo.RECEPCION,
                "PRE",
                "SUF",
                1,
                null,
                null
        );

        Consecutivo consecutivoExistente = new Consecutivo("PRE", "SUF", 1, TipoConsecutivo.RECEPCION);
        consecutivoExistente.setId(UUID.randomUUID());

        when(consecutivoRepository.findByExample(any(Consecutivo.class))).thenReturn(Optional.of(consecutivoExistente));

        // Act & Assert
        assertThrows(ConsecutivoUnicoTipoException.class, () -> useCase.execute(command));
        verify(consecutivoRepository).findByExample(any(Consecutivo.class));
        verify(consecutivoRepository, never()).save(any(Consecutivo.class));
        verify(mapper, never()).toDto(any(Consecutivo.class));
    }

    @Test
    @DisplayName("Debe lanzar excepción cuando TipoDocumental es null en consecutivo de producción")
    void debeLanzarExcepcionCuandoTipoDocumentalEsNullEnConsecutivoProduccion() {
        // Arrange
        CrearConsecutivoCommand command = new CrearConsecutivoCommand(
                TipoConsecutivo.PRODUCCION,
                "PRE",
                "SUF",
                1,
                tipoDocumentalId,
                clasificacionDocumentalId
        );

        when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.empty());
        when(clasificacionDocumentalRepository.findById(clasificacionDocumentalId)).thenReturn(Optional.of(clasificacionDocumental));

        // Act & Assert
        assertThrows(ConsecutivoWithoutTipoDocumentalException.class, () -> useCase.execute(command));

        verify(tipoDocumentalRepository).findById(tipoDocumentalId);
        verify(clasificacionDocumentalRepository).findById(clasificacionDocumentalId);
        verify(consecutivoRepository, never()).save(any(Consecutivo.class));
        verify(mapper, never()).toDto(any(Consecutivo.class));
    }

    @Test
    @DisplayName("Debe crear consecutivo de producción cuando ClasificacionDocumental es null")
    void debeCrearConsecutivoProduccionCuandoClasificacionDocumentalEsNull() {
        // Arrange
        CrearConsecutivoCommand command = new CrearConsecutivoCommand(
                TipoConsecutivo.PRODUCCION,
                "PRE",
                "SUF",
                1,
                tipoDocumentalId,
                clasificacionDocumentalId
        );

        Consecutivo consecutivoGuardado = new Consecutivo("PRE", "SUF", 1, tipoDocumental, null);
        consecutivoGuardado.setId(UUID.randomUUID());

        when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(tipoDocumental));
        when(clasificacionDocumentalRepository.findById(clasificacionDocumentalId)).thenReturn(Optional.empty());
        when(consecutivoRepository.findByExample(any(Consecutivo.class))).thenReturn(Optional.empty());
        when(consecutivoRepository.save(any(Consecutivo.class))).thenReturn(consecutivoGuardado);
        when(mapper.toDto(any(Consecutivo.class))).thenReturn(consecutivoDTO);

        // Act
        ConsecutivoDTO resultado = useCase.execute(command);

        // Assert
        verify(tipoDocumentalRepository).findById(tipoDocumentalId);
        verify(clasificacionDocumentalRepository).findById(clasificacionDocumentalId);
        verify(consecutivoRepository).findByExample(any(Consecutivo.class));
        verify(consecutivoRepository).save(any(Consecutivo.class));
        verify(mapper).toDto(any(Consecutivo.class));

        assertNotNull(resultado);
    }
} 
