package co.com.gedsys.base.application.usecase.planeacion.seccion;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;

@ExtendWith(MockitoExtension.class)
class AgregarSeccionUseCaseTest {

    @Mock
    private SeccionRepository repository;

    @Mock
    private SeccionUseCaseMapper mapper;

    private AgregarSeccionUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new AgregarSeccionUseCase(repository, mapper);
    }

    @Test
    void deberiaPermitirRegistrarPrimerNodoPrincipal() {
        // Arrange
        var command = new AgregarSeccionCommand("01.00.00", "Sección Principal", "Responsable 1", null);
        var seccion = new Seccion("01.00.00", "Sección Principal");
        var seccionDTO = new SeccionDTO(UUID.randomUUID().toString(), EstadoSeccion.ACTIVA.name(), null, "01.00.00",
                "Sección Principal", "Responsable 1");
        when(repository.findByCode(anyString())).thenReturn(Optional.empty());
        when(repository.buscarNodoPrincipalDelOrganigrama()).thenReturn(Optional.empty());
        when(repository.save(any(Seccion.class))).thenReturn(seccion);
        when(mapper.toDto(any(Seccion.class))).thenReturn(seccionDTO);

        // Act
        SeccionDTO result = useCase.execute(command);

        // Assert
        assertNotNull(result);
        assertEquals("01.00.00", result.codigo());
        assertEquals("Sección Principal", result.nombre());
        assertEquals("Responsable 1", result.responsable());
        verify(repository).findByCode("01.00.00");
        verify(repository).buscarNodoPrincipalDelOrganigrama();
        verify(repository).save(any(Seccion.class));
    }

    @Test
    void noDeberiaPermitirRegistrarSegundoNodoPrincipal() {
        // Arrange
        var command = new AgregarSeccionCommand("02.00.00", "Segunda Sección Principal", "Responsable 2", null);
        var seccionExistente = new Seccion("01.00.00", "Primera Sección Principal");

        when(repository.findByCode(anyString())).thenReturn(Optional.empty());
        when(repository.buscarNodoPrincipalDelOrganigrama()).thenReturn(Optional.of(seccionExistente));

        // Act & Assert
        var exception = assertThrows(NodoPrincipalOrganigramaException.class, () -> useCase.execute(command));
        assertEquals("Ya existe un nodo principal activo en el organigrama", exception.getMessage());
        verify(repository).findByCode("02.00.00");
        verify(repository).buscarNodoPrincipalDelOrganigrama();
        verify(repository, never()).save(any(Seccion.class));
    }

    @Test
    void deberiaPermitirRegistrarSeccionConPadreValido() {
        // Arrange
        UUID padreId = UUID.randomUUID();
        var command = new AgregarSeccionCommand("02.00.00", "Sección Hijo", "Responsable 3", padreId);
        var seccionPadre = new Seccion("01.00.00", "Sección Principal");
        var seccionHijo = new Seccion("02.00.00", "Sección Hijo");
        var seccionDTO = new SeccionDTO(UUID.randomUUID().toString(), EstadoSeccion.ACTIVA.name(),
                new SeccionDTO(padreId.toString(), EstadoSeccion.ACTIVA.name(), null, "01.00.00", "Sección Principal", "Responsable 1"),
                "02.00.00", "Sección Hijo", "Responsable 3");

        when(repository.findByCode(anyString())).thenReturn(Optional.empty());
        when(repository.findById(padreId)).thenReturn(Optional.of(seccionPadre));
        when(repository.save(any(Seccion.class))).thenReturn(seccionHijo);
        when(mapper.toDto(any(Seccion.class))).thenReturn(seccionDTO);

        // Act
        SeccionDTO result = useCase.execute(command);

        // Assert
        assertNotNull(result);
        assertEquals("02.00.00", result.codigo());
        assertEquals("Sección Hijo", result.nombre());
        assertEquals("Responsable 3", result.responsable());
        assertNotNull(result.padre());
        verify(repository).findByCode("02.00.00");
        verify(repository).findById(padreId);
        verify(repository).save(any(Seccion.class));
    }

    @Test
    void deberiaLanzarExcepcionSiCodigoYaExiste() {
        // Arrange
        var command = new AgregarSeccionCommand("01.00.00", "Sección", "Responsable 4", null);
        var seccionExistente = new Seccion("01.00.00", "Sección Existente");

        when(repository.findByCode(anyString())).thenReturn(Optional.of(seccionExistente));

        // Act & Assert
        var exception = assertThrows(EntityAlreadyExistsException.class, () -> useCase.execute(command));
        assertEquals("Unidad productora con el código '01.00.00' ya existe", exception.getMessage());
        verify(repository).findByCode("01.00.00");
        verify(repository, never()).save(any(Seccion.class));
    }
}