package co.com.gedsys.base.application.usecase.produccion.radicacion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoProducidoCommand;
import co.com.gedsys.base.application.usecase.produccion.radicacion.exception.PoliticaUnicoRadicadoDeProduccionException;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RadicarDocumentoProducidoUseCaseTest {

    @Mock
    private DocumentoRepository documentoRepository;
    @Mock
    private ConsecutivoRepository consecutivoRepository;
    @Mock
    private RadicadoRepository radicadoRepository;
    @Mock
    private RadicadoApplicationLayerMapper radicadoMapper;

    private RadicarDocumentoProducidoUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new RadicarDocumentoProducidoUseCase(
            documentoRepository,
            consecutivoRepository,
            radicadoRepository,
            radicadoMapper
        );
    }

    @Nested
    @DisplayName("Dado un documento válido")
    class DocumentoValido {
        private final UUID documentoId = UUID.randomUUID();
        private final UUID clasificacionId = UUID.randomUUID();
        private final UUID tipoDocumentalId = UUID.randomUUID();
        private Documento documento;
        private RadicarDocumentoProducidoCommand command;

        @BeforeEach
        void configurar() {
            TipoDocumental tipoDocumental = new TipoDocumental("Tipo Test");
            tipoDocumental.setId(tipoDocumentalId);

            Seccion seccion = mock(Seccion.class);
            SerieDocumental serie = mock(SerieDocumental.class);
            ClasificacionDocumental clasificacion = new ClasificacionDocumental(seccion, serie);
            clasificacion.setId(clasificacionId);

            UnidadDocumental unidadDocumental = new UnidadDocumental("Unidad Test", clasificacion);

            documento = new Documento("Titulo Test", "file-123", tipoDocumental, unidadDocumental, 
                "autor.test", new HashSet<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>());

            command = new RadicarDocumentoProducidoCommand(documentoId);
        }

        @Test
        @DisplayName("debería radicar exitosamente cuando no tiene radicados previos")
        void deberiaRadicarExitosamente() {
            when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
            when(radicadoRepository.buscarRadicadosDeDocumento(documentoId)).thenReturn(new ArrayList<>());
            when(consecutivoRepository.buscarConsecutivosParaTipoDocumental(any())).thenReturn(new ArrayList<>());

            Consecutivo consecutivo = new Consecutivo(null, null, 0, documento.getTipoDocumental(), null);
            when(consecutivoRepository.save(any())).thenReturn(consecutivo);
            when(radicadoMapper.toDTO(any())).thenReturn(new RadicadoDTO(null, 1, "autor.test", 
                "2024-03-19", null, null, null, "PRODUCCION", null, null, null, null));

            RadicadoDTO resultado = useCase.execute(command);

            assertNotNull(resultado);
            verify(radicadoRepository).save(any());
            verify(consecutivoRepository).save(any());
        }

        @Test
        @DisplayName("debería fallar cuando ya tiene un radicado de producción activo")
        void deberiaFallarCuandoTieneRadicadoActivo() {
            Consecutivo consecutivo = new Consecutivo(null, null, 0, documento.getTipoDocumental(), null);
            Radicado radicadoExistente = new Radicado(consecutivo);
            radicadoExistente.expedir(documento);

            when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
            when(radicadoRepository.buscarRadicadosDeDocumento(documentoId))
                .thenReturn(List.of(radicadoExistente));

            assertThrows(PoliticaUnicoRadicadoDeProduccionException.class,
                () -> useCase.execute(command));
        }
    }

    @Nested
    @DisplayName("Dado un documento inválido")
    class DocumentoInvalido {

        @Test
        @DisplayName("debería fallar cuando el documento no existe")
        void deberiaFallarCuandoDocumentoNoExiste() {
            UUID documentoId = UUID.randomUUID();
            RadicarDocumentoProducidoCommand command = new RadicarDocumentoProducidoCommand(documentoId);

            when(documentoRepository.findById(documentoId)).thenReturn(Optional.empty());

            assertThrows(EntityNotExistsException.class,
                () -> useCase.execute(command));
        }
    }

    @Nested
    @DisplayName("Dado diferentes escenarios de consecutivos")
    class ConsecutivosEscenarios {
        private UUID documentoId;
        private Documento documento;
        private RadicarDocumentoProducidoCommand command;

        @BeforeEach
        void configurar() {
            documentoId = UUID.randomUUID();

            TipoDocumental tipoDocumental = new TipoDocumental("Tipo Test");
            tipoDocumental.setId(UUID.randomUUID());

            Seccion seccion = mock(Seccion.class);
            SerieDocumental serie = mock(SerieDocumental.class);
            ClasificacionDocumental clasificacion = new ClasificacionDocumental(seccion, serie);
            clasificacion.setId(UUID.randomUUID());

            UnidadDocumental unidadDocumental = new UnidadDocumental("Unidad Test", clasificacion);

            documento = new Documento("Titulo Test", "file-123", tipoDocumental, unidadDocumental, 
                "autor.test", new HashSet<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>());

            command = new RadicarDocumentoProducidoCommand(documentoId);

            when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documento));
            when(radicadoRepository.buscarRadicadosDeDocumento(documentoId)).thenReturn(new ArrayList<>());
        }

        @Test
        @DisplayName("debería usar consecutivo específico cuando existe")
        void deberiaUsarConsecutivoEspecifico() {
            Consecutivo consecutivoEspecifico = new Consecutivo(null, null, 0, 
                documento.getTipoDocumental(), documento.getUnidadDocumental().getClasificacion());

            when(consecutivoRepository.buscarConsecutivosParaTipoDocumental(any()))
                .thenReturn(List.of(consecutivoEspecifico));
            when(radicadoMapper.toDTO(any())).thenReturn(new RadicadoDTO(null, 1, "autor.test", 
                "2024-03-19", null, null, null, "PRODUCCION", null, null, null, null));

            useCase.execute(command);

            verify(consecutivoRepository).save(consecutivoEspecifico);
        }

        @Test
        @DisplayName("debería crear nuevo consecutivo cuando no existe ninguno")
        void deberiaCrearNuevoConsecutivo() {
            when(consecutivoRepository.buscarConsecutivosParaTipoDocumental(any()))
                .thenReturn(new ArrayList<>());

            Consecutivo nuevoConsecutivo = new Consecutivo(null, null, 0, 
                documento.getTipoDocumental(), null);
            when(consecutivoRepository.save(any())).thenReturn(nuevoConsecutivo);
            when(radicadoMapper.toDTO(any())).thenReturn(new RadicadoDTO(null, 1, "autor.test", 
                "2024-03-19", null, null, null, "PRODUCCION", null, null, null, null));

            useCase.execute(command);

            verify(consecutivoRepository).save(argThat(consecutivo ->
                consecutivo.getClasificacionDocumental() == null &&
                consecutivo.getTipoDocumental() != null
            ));
        }
    }
}
