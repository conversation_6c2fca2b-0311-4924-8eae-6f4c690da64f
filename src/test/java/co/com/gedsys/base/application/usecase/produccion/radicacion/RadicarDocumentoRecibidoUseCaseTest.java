package co.com.gedsys.base.application.usecase.produccion.radicacion;

import co.com.gedsys.base.application.dto.AnexoDocumentoDTO;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.PropiedadRadicadoDTO;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoRecibidoCommand;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RadicarDocumentoRecibidoUseCaseTest {

    @Mock
    private DocumentoRepository documentoRepository;

    @Mock
    private TipoDocumentalRepository tipoDocumentalRepository;

    @Mock
    private UnidadDocumentalRepository unidadDocumentalRepository;

    @Mock
    private DefinicionMetadatosRepository definicionMetadatosRepository;

    @Mock
    private ConsecutivoRepository consecutivoRepository;

    @Mock
    private RadicadoRepository radicadoRepository;

    @Mock
    private RadicadoApplicationLayerMapper radicadoMapper;

    @Captor
    private ArgumentCaptor<Documento> documentoCaptor;

    @Captor
    private ArgumentCaptor<Radicado> radicadoCaptor;

    @Captor
    private ArgumentCaptor<Consecutivo> consecutivoCaptor;

    private RadicarDocumentoRecibidoUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new RadicarDocumentoRecibidoUseCase(
            documentoRepository,
            tipoDocumentalRepository,
            unidadDocumentalRepository,
            definicionMetadatosRepository,
            consecutivoRepository,
            radicadoRepository,
            radicadoMapper
        );
    }

    @Test
    @DisplayName("Debería crear un documento nuevo y expedir un radicado con consecutivo existente")
    void deberiaCrearDocumentoYExpedirRadicadoConConsecutivoExistente() {
        // Arrange
        UUID tipoDocumentalId = UUID.randomUUID();
        UUID unidadDocumentalId = UUID.randomUUID();

        TipoDocumental tipoDocumental = createTipoDocumental(tipoDocumentalId);
        UnidadDocumental unidadDocumental = createUnidadDocumental(unidadDocumentalId);

        Consecutivo consecutivo = new Consecutivo("REC", "2023", 1, TipoConsecutivo.RECEPCION);
        consecutivo.setId(UUID.randomUUID());

        Documento documentoGuardado = createDocumento(tipoDocumental, unidadDocumental);

        Radicado radicadoExpedido = new Radicado(consecutivo);
        radicadoExpedido.expedir(documentoGuardado);

        RadicadoDTO radicadoDTO = createRadicadoDTO();

        // Configuración de mocks
        when(tipoDocumentalRepository.findById(UUID.fromString(tipoDocumentalId.toString()))).thenReturn(Optional.of(tipoDocumental));
        when(unidadDocumentalRepository.findById(UUID.fromString(unidadDocumentalId.toString()))).thenReturn(Optional.of(unidadDocumental));
        when(definicionMetadatosRepository.buscarPorPatrones(any())).thenReturn(Collections.emptySet());
        when(documentoRepository.save(any(Documento.class))).thenReturn(documentoGuardado);
        when(consecutivoRepository.findByExample(any(Consecutivo.class))).thenReturn(Optional.of(consecutivo));
        // No llamamos al save del consecutivo en este caso
        doReturn(consecutivo).when(consecutivoRepository).save(any(Consecutivo.class));
        when(radicadoRepository.save(any(Radicado.class))).thenReturn(radicadoExpedido);
        when(radicadoMapper.toDTO(any(Radicado.class))).thenReturn(radicadoDTO);

        RadicarDocumentoRecibidoCommand command = createCommand(tipoDocumentalId, unidadDocumentalId);

        // Act
        RadicadoDTO resultado = useCase.execute(command);

        // Assert
        verify(documentoRepository).save(documentoCaptor.capture());
        verify(radicadoRepository).save(radicadoCaptor.capture());

        Documento documentoCreado = documentoCaptor.getValue();
        Radicado radicadoCreado = radicadoCaptor.getValue();

        assertThat(documentoCreado.getTitulo()).isEqualTo("Documento Prueba");
        assertThat(documentoCreado.getFileId()).isEqualTo("file-123");
        assertThat(documentoCreado.getAutor()).isEqualTo("autor");

        assertThat(radicadoCreado.getEstado().name()).isEqualTo("ACTIVO");
        assertThat(radicadoCreado.getTipo()).isEqualTo(TipoConsecutivo.RECEPCION);
        assertThat(radicadoCreado.getDocumento()).isNotNull();

        assertThat(resultado).isNotNull();
        assertThat(resultado.numeroRadicado()).isEqualTo(1);
    }

    @Test
    @DisplayName("Debería crear un documento nuevo y expedir un radicado con un nuevo consecutivo")
    void deberiaCrearDocumentoYExpedirRadicadoConNuevoConsecutivo() {
        // Arrange
        UUID tipoDocumentalId = UUID.randomUUID();
        UUID unidadDocumentalId = UUID.randomUUID();

        TipoDocumental tipoDocumental = createTipoDocumental(tipoDocumentalId);
        UnidadDocumental unidadDocumental = createUnidadDocumental(unidadDocumentalId);

        Consecutivo consecutivoCreado = new Consecutivo("REC", "2023", 1, TipoConsecutivo.RECEPCION);
        consecutivoCreado.setId(UUID.randomUUID());

        Documento documentoGuardado = createDocumento(tipoDocumental, unidadDocumental);

        Radicado radicadoExpedido = new Radicado(consecutivoCreado);
        radicadoExpedido.expedir(documentoGuardado);

        RadicadoDTO radicadoDTO = createRadicadoDTO();

        // Configuración de mocks
        when(tipoDocumentalRepository.findById(UUID.fromString(tipoDocumentalId.toString()))).thenReturn(Optional.of(tipoDocumental));
        when(unidadDocumentalRepository.findById(UUID.fromString(unidadDocumentalId.toString()))).thenReturn(Optional.of(unidadDocumental));
        when(definicionMetadatosRepository.buscarPorPatrones(any())).thenReturn(Collections.emptySet());
        when(documentoRepository.save(any(Documento.class))).thenReturn(documentoGuardado);
        when(consecutivoRepository.findByExample(any(Consecutivo.class))).thenReturn(Optional.empty());
        // Se llama dos veces al save en esta implementación
        when(consecutivoRepository.save(any(Consecutivo.class))).thenReturn(consecutivoCreado);
        when(radicadoRepository.save(any(Radicado.class))).thenReturn(radicadoExpedido);
        when(radicadoMapper.toDTO(any(Radicado.class))).thenReturn(radicadoDTO);

        RadicarDocumentoRecibidoCommand command = createCommand(tipoDocumentalId, unidadDocumentalId);

        // Act
        RadicadoDTO resultado = useCase.execute(command);

        // Assert
        verify(documentoRepository).save(documentoCaptor.capture());
        // Verificar que se llama al repositorio al menos una vez, sin especificar el número exacto
        verify(consecutivoRepository, atLeastOnce()).save(consecutivoCaptor.capture());
        verify(radicadoRepository).save(radicadoCaptor.capture());

        Documento documentoCreado = documentoCaptor.getValue();
        Consecutivo nuevoConsecutivo = consecutivoCaptor.getValue();
        Radicado radicadoCreado = radicadoCaptor.getValue();

        assertThat(documentoCreado.getTitulo()).isEqualTo("Documento Prueba");
        assertThat(nuevoConsecutivo.getTipoConsecutivo()).isEqualTo(TipoConsecutivo.RECEPCION);
        assertThat(radicadoCreado.getDocumento()).isNotNull();

        assertThat(resultado).isNotNull();
        assertThat(resultado.numeroRadicado()).isEqualTo(1);
    }

    private TipoDocumental createTipoDocumental(UUID id) {
        TipoDocumental tipoDocumental = new TipoDocumental("Tipo Documental Test");
        tipoDocumental.setId(id);
        return tipoDocumental;
    }

    private UnidadDocumental createUnidadDocumental(UUID id) {
        // Mock de ClasificacionDocumental para el test
        ClasificacionDocumental clasificacion = mock(ClasificacionDocumental.class);
        // Ya no hacemos stubbing innecesario

        UnidadDocumental unidadDocumental = new UnidadDocumental("Unidad Test", clasificacion);
        unidadDocumental.setId(id);
        return unidadDocumental;
    }

    private Documento createDocumento(TipoDocumental tipoDocumental, UnidadDocumental unidadDocumental) {
        return new Documento(
            "Documento Prueba",
            "file-123",
            tipoDocumental,
            unidadDocumental,
            "autor",
            Collections.emptySet(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList()
        );
    }

    private RadicadoDTO createRadicadoDTO() {
        return new RadicadoDTO(
            UUID.randomUUID().toString(),
            1,
            "autor",
            "2023-01-01",
            null,
            "REC",
            "2023",
            "RECEPCION",
            null,
            null,
            null,
            null
        );
    }

    private RadicarDocumentoRecibidoCommand createCommand(UUID tipoDocumentalId, UUID unidadDocumentalId) {
        return new RadicarDocumentoRecibidoCommand(
            "Documento Prueba",
            "file-123",
            tipoDocumentalId.toString(),
            unidadDocumentalId.toString(),
            "autor",
            List.of(new MetadatoDocumentoDTO("nombre", "valor")),
            new PropiedadRadicadoDTO(100, 1, 200, 50, 50, 0),
            List.of(new AnexoDocumentoDTO(
                "anexo1", 
                "descripcion", 
                UUID.randomUUID().toString(), 
                "999b5233407e71fb28c80c218006341179cd67a83b64a56c0ac25064e094c441", 
                1000L, 
                "pdf"))
        );
    }
} 
