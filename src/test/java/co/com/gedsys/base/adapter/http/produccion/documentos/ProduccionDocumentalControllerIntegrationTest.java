package co.com.gedsys.base.adapter.http.produccion.documentos;

import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Pruebas de integración para ProduccionDocumentalController.
 * Estas pruebas utilizan TestContainers para crear un contenedor de PostgreSQL
 * y probar la integración completa del controlador con la base de datos.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@Testcontainers
@ActiveProfiles("test")
@Transactional
public class ProduccionDocumentalControllerIntegrationTest {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16-alpine")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");

    /**
     * Configuración dinámica de propiedades para Spring Boot.
     */
    @DynamicPropertySource
    static void registerPgProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        // Deshabilitar Flyway para esta prueba
        registry.add("spring.flyway.enabled", () -> "false");
        // Configurar Hibernate para crear el esquema
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private TipoDocumentalRepository tipoDocumentalRepository;

    @Autowired
    private UnidadDocumentalRepository unidadDocumentalRepository;

    @Autowired
    private DocumentoRepository documentoRepository;

    @Autowired
    private RadicadoRepository radicadoRepository;

    @Autowired
    private SeccionRepository seccionRepository;

    @Autowired
    private SerieDocumentalRepository serieDocumentalRepository;

    @Autowired
    private ClasificacionDocumentalRepository clasificacionDocumentalRepository;

    @Autowired
    private ExternalUsersRepository externalUsersRepository;

    @Autowired
    private co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository definicionMetadatosRepository;

    private ObjectMapper objectMapper;
    private UUID tipoDocumentalId;
    private UUID unidadDocumentalId;
    private UUID destinatarioId;
    private UUID seccionId;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();

        // Crear entidades necesarias en la base de datos
        crearEntidadesNecesarias();
    }

    /**
     * Crea las entidades necesarias en la base de datos para las pruebas.
     * 
     * Este método crea todas las entidades requeridas para el test en el orden correcto:
     * 1. Sección (unidad organizativa)
     * 2. Serie Documental (clasificación de documentos)
     * 3. Clasificación Documental (relación entre Sección y Serie Documental)
     * 4. Tipo Documental (tipo de documento)
     * 5. Unidad Documental (agrupación de documentos con la misma clasificación)
     * 
     * Estas entidades son necesarias para poder recibir un documento a través del endpoint.
     */
    private void crearEntidadesNecesarias() {
        try {
            // 1. Crear sección (unidad organizativa)
            Seccion seccion = new Seccion("01.00.00", "Despacho del Alcalde");
            seccion.setResponsable("jmarin");
            seccion = seccionRepository.save(seccion);
            seccionId = seccion.getId();

            // 2. Crear serie documental (clasificación de documentos)
            SerieDocumental serieDocumental = new SerieDocumental("01", "Comunicaciones Oficiales");
            serieDocumentalRepository.guardar(serieDocumental);

            // Buscar la serie documental por código para obtener la entidad con ID
            serieDocumental = serieDocumentalRepository.findByCode("01")
                .orElseThrow(() -> new RuntimeException("No se pudo encontrar la serie documental recién creada"));

            // 3. Crear clasificación documental (relación entre Sección y Serie Documental)
            ClasificacionDocumental clasificacionDocumental = new ClasificacionDocumental(seccion, serieDocumental);
            clasificacionDocumental = clasificacionDocumentalRepository.save(clasificacionDocumental);

            // 4. Crear tipo documental (tipo de documento)
            TipoDocumental tipoDocumental = new TipoDocumental("Carta");
            tipoDocumental = tipoDocumentalRepository.save(tipoDocumental);
            tipoDocumentalId = tipoDocumental.getId();

            // 5. Crear unidad documental (agrupación de documentos con la misma clasificación)
            UnidadDocumental unidadDocumental = new UnidadDocumental("Actas de Gobierno", clasificacionDocumental);
            unidadDocumental = unidadDocumentalRepository.save(unidadDocumental);
            unidadDocumentalId = unidadDocumental.getId();

            // 6. Crear usuario externo (destinatario para prueba de radicación de envío)
            ExternalUser destinatario = new ExternalUser("Destinatario Prueba", "CC", "123456789");
            destinatario.setProperties(new ArrayList<>()); // Inicializar la lista de propiedades
            destinatario.setStatus(ExternalUserStatus.ACTIVO); // Establecer el estado como ACTIVO
            destinatario = externalUsersRepository.save(destinatario);
            destinatarioId = destinatario.getId();

            // 7. Crear definición de metadato para "Folios"
            co.com.gedsys.base.domain.metadato.DefinicionMetadato definicionFolios = 
                co.com.gedsys.base.domain.metadato.DefinicionMetadato.create(
                    "Folios", 
                    co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.CONTENIDO,
                    co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum.NUMERICO
                );
            definicionMetadatosRepository.save(definicionFolios);

            System.out.println("[DEBUG_LOG] Entidades creadas correctamente:");
            System.out.println("[DEBUG_LOG] - Sección: " + seccion.getId() + " - " + seccion.getNombre());
            System.out.println("[DEBUG_LOG] - Serie Documental: " + serieDocumental.getId() + " - " + serieDocumental.getNombre());
            System.out.println("[DEBUG_LOG] - Clasificación Documental: " + clasificacionDocumental.getId());
            System.out.println("[DEBUG_LOG] - Tipo Documental: " + tipoDocumentalId + " - " + tipoDocumental.nombre());
            System.out.println("[DEBUG_LOG] - Unidad Documental: " + unidadDocumentalId + " - " + unidadDocumental.nombre());
            System.out.println("[DEBUG_LOG] - Destinatario: " + destinatarioId + " - " + destinatario.getName());
        } catch (Exception e) {
            System.err.println("[DEBUG_LOG] Error al crear entidades: " + e.getMessage());
            e.printStackTrace();
            throw e; // Re-lanzar la excepción para que el test falle
        }
    }

    /**
     * Prueba para el endpoint recibirDocumento.
     * Verifica que se pueda recibir un documento y crear un radicado.
     */
    @Test
    @DisplayName("Debería recibir un documento y crear un radicado")
    public void testRecibirDocumento() throws Exception {
        // Crear solicitud para recibir un documento
        SolicitudRecepcionDocumento request = crearSolicitudRecepcionDocumento();

        // Realizar la petición POST
        ResultActions result = mockMvc.perform(post("/api/v1/produccion/documentos/recibir")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)));

        // Verificar la respuesta
        MvcResult mvcResult = result.andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", notNullValue()))
                .andExpect(jsonPath("$.numeroRadicado", notNullValue()))
                .andExpect(jsonPath("$.tipo", is("RECEPCION")))
                .andReturn();

        // Obtener el radicado de la respuesta
        String responseContent = mvcResult.getResponse().getContentAsString();
        RadicadoDTO radicadoDTO = objectMapper.readValue(responseContent, RadicadoDTO.class);

        // Verificar que el radicado existe en la base de datos
        assertThat(radicadoRepository.findById(UUID.fromString(radicadoDTO.id()))).isPresent();

        // Verificar que el documento existe en la base de datos
        assertThat(documentoRepository.findAll()).isNotEmpty();
    }

    /**
     * Crea una solicitud válida para el endpoint recibirDocumento.
     */
    private SolicitudRecepcionDocumento crearSolicitudRecepcionDocumento() {
        return new SolicitudRecepcionDocumento(
                "Documento de Prueba",
                UUID.randomUUID().toString(), // fileId
                tipoDocumentalId.toString(),
                unidadDocumentalId.toString(),
                "autor.prueba",
                List.of(new EstructuraMetadatoDocumento("Folios", "10", TipoMetadatoEnum.CONTENIDO)),
                new EstructuraPropiedadesRadicado(100, 1, 200, 50, 50, 0),
                List.of(new EstructuraAnexoDocumento(
                        "anexo1",
                        "Descripción del anexo",
                        UUID.randomUUID().toString(),
                        "999b5233407e71fb28c80c218006341179cd67a83b64a56c0ac25064e094c441",
                        1000L,
                        "pdf")),
                destinatarioId.toString(), // remitenteId
                seccionId.toString(), // destinoId
                null // destinatarioInternoId
        );
    }

    /**
     * Prueba para el endpoint crearBorrador.
     * Verifica que se pueda crear un borrador de documento.
     */
    @Test
    @DisplayName("Debería crear un borrador de documento")
    public void testCrearBorrador() throws Exception {
        // Crear solicitud para crear un borrador
        SolicitudRegistroDocumento request = crearSolicitudRegistroDocumento();

        // Realizar la petición POST
        ResultActions result = mockMvc.perform(post("/api/v1/produccion/documentos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)));

        // Verificar la respuesta
        MvcResult mvcResult = result.andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", notNullValue()))
                .andExpect(jsonPath("$.titulo", is(request.titulo())))
                .andExpect(jsonPath("$.autor", is(request.autor())))
                .andReturn();

        // Obtener el documento de la respuesta
        String responseContent = mvcResult.getResponse().getContentAsString();
        RespuestaDocumentoProducido respuesta = objectMapper.readValue(responseContent, RespuestaDocumentoProducido.class);

        // Verificar que el documento existe en la base de datos
        assertThat(documentoRepository.findById(respuesta.id())).isPresent();
    }

    /**
     * Crea una solicitud válida para el endpoint crearBorrador.
     */
    private SolicitudRegistroDocumento crearSolicitudRegistroDocumento() {
        return new SolicitudRegistroDocumento(
                "Borrador de Prueba",
                UUID.randomUUID().toString(), // fileId
                tipoDocumentalId,
                unidadDocumentalId,
                "autor.prueba",
                List.of(new EstructuraMetadatoDocumento("Folios", "10", TipoMetadatoEnum.CONTENIDO)),
                List.of(new SolicitudRegistroDocumento.FirmaUsuario(
                        "firmante.prueba",
                        100,
                        "Observaciones de firma",
                        1,
                        200,
                        50,
                        50
                )),
                List.of(new SolicitudRegistroDocumento.Aprobacion("aprobador.prueba")),
                List.of(new SolicitudRegistroDocumento.RegistroAnexo(
                        "anexo1",
                        "Descripción del anexo",
                        UUID.randomUUID().toString(),
                        "999b5233407e71fb28c80c218006341179cd67a83b64a56c0ac25064e094c441",
                        1000L,
                        "pdf"
                ))
        );
    }

    /**
     * Prueba para el endpoint radicarDocumentoEnvioDocumental.
     * Verifica que se pueda radicar un documento como envío con un destinatario.
     */
    @Test
    @DisplayName("Debería radicar un documento como envío con destinatario")
    public void testRadicarDocumentoEnvioDocumental() throws Exception {
        // 1. Crear un borrador de documento
        SolicitudRegistroDocumento solicitudBorrador = crearSolicitudRegistroDocumento();

        // Realizar la petición POST para crear el borrador
        MvcResult resultadoBorrador = mockMvc.perform(post("/api/v1/produccion/documentos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(solicitudBorrador)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id", notNullValue()))
                .andReturn();

        // Obtener el ID del documento creado
        String responseBorrador = resultadoBorrador.getResponse().getContentAsString();
        RespuestaDocumentoProducido documentoCreado = objectMapper.readValue(responseBorrador, RespuestaDocumentoProducido.class);
        UUID documentoId = documentoCreado.id();

        System.out.println("[DEBUG_LOG] Documento creado con ID: " + documentoId);

        // Verificar que el destinatario existe
        System.out.println("[DEBUG_LOG] Verificando destinatario con ID: " + destinatarioId);
        var destinatario = externalUsersRepository.findById(destinatarioId);
        System.out.println("[DEBUG_LOG] Destinatario encontrado: " + (destinatario.isPresent() ? "SÍ" : "NO"));
        if (destinatario.isPresent()) {
            System.out.println("[DEBUG_LOG] Nombre del destinatario: " + destinatario.get().getName());
            System.out.println("[DEBUG_LOG] Estado del destinatario: " + destinatario.get().getStatus());
            System.out.println("[DEBUG_LOG] Tipo de identificación: " + destinatario.get().getIdentificationType());
            System.out.println("[DEBUG_LOG] Número de identificación: " + destinatario.get().getIdentificationNumber());

            // Forzar el estado a ACTIVO nuevamente para asegurarnos
            ExternalUser userToUpdate = destinatario.get();
            userToUpdate.setStatus(ExternalUserStatus.ACTIVO);
            externalUsersRepository.save(userToUpdate);

            // Verificar que se guardó correctamente
            var destinatarioActualizado = externalUsersRepository.findById(destinatarioId);
            System.out.println("[DEBUG_LOG] Estado del destinatario después de actualizar: " + 
                (destinatarioActualizado.isPresent() ? destinatarioActualizado.get().getStatus() : "NO ENCONTRADO"));
        }

        // 2. Crear solicitud para radicar el documento como envío
        SolicitudDeEnvio solicitudRadicacion = new SolicitudDeEnvio(
            destinatarioId,
            List.of(new EstructuraMetadatoDocumento("Folios", "10", TipoMetadatoEnum.CONTENIDO)),
            new EstructuraPropiedadesRadicado(100, 1, 200, 50, 50, 0)
        );
        System.out.println("[DEBUG_LOG] Solicitud de radicación creada con destinatarioId: " + solicitudRadicacion.destinatarioId());

        try {
            // Realizar la petición POST para radicar el documento como envío
            MvcResult result = mockMvc.perform(post("/api/v1/produccion/documentos/{documentId}/radicar-envio-documental", documentoId)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(solicitudRadicacion)))
                    .andReturn();

            int status = result.getResponse().getStatus();
            String responseContent = result.getResponse().getContentAsString();

            System.out.println("[DEBUG_LOG] Status code: " + status);
            System.out.println("[DEBUG_LOG] Respuesta: " + responseContent);

            if (status == 200) {
                System.out.println("[DEBUG_LOG] Radicación exitosa");
            } else {
                System.out.println("[DEBUG_LOG] Radicación fallida con status: " + status);
                System.out.println("[DEBUG_LOG] Respuesta de error: " + responseContent);
                // Intentar extraer más información del error
                if (responseContent.contains("error")) {
                    System.out.println("[DEBUG_LOG] Detalle del error: " + responseContent);
                }
            }
        } catch (Exception e) {
            System.out.println("[DEBUG_LOG] Error al radicar documento: " + e.getMessage());
            Throwable cause = e.getCause();
            while (cause != null) {
                System.out.println("[DEBUG_LOG] Causa: " + cause.getMessage());
                cause = cause.getCause();
            }
            e.printStackTrace();
            throw e;
        }
    }

    @Test
    @DisplayName("Debería cargar un documento correctamente")
    public void testCargarDocumento() throws Exception {
        // Crear solicitud para cargar un documento
        SolicitudRegistroDocumento request = crearSolicitudCargaDocumento();

        // Realizar la petición POST
        ResultActions result = mockMvc.perform(post("/api/v1/produccion/documentos/cargar")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)));

        // Verificar la respuesta
        MvcResult mvcResult = result.andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", notNullValue()))
                .andExpect(jsonPath("$.titulo", is(request.titulo())))
                .andExpect(jsonPath("$.autor", is(request.autor())))
                .andReturn();

        // Obtener el documento de la respuesta
        String responseContent = mvcResult.getResponse().getContentAsString();
        RespuestaDocumentoProducido respuesta = objectMapper.readValue(responseContent, RespuestaDocumentoProducido.class);

        // Verificar que el documento existe en la base de datos
        assertThat(documentoRepository.findById(respuesta.id())).isPresent();
    }

    /**
     * Crea una solicitud válida para el endpoint cargarDocumento.
     * A diferencia de crearSolicitudRegistroDocumento, esta solicitud no incluye firmas ni aprobaciones.
     */
    private SolicitudRegistroDocumento crearSolicitudCargaDocumento() {
        return new SolicitudRegistroDocumento(
                "Documento Cargado de Prueba",
                UUID.randomUUID().toString(), // fileId
                tipoDocumentalId,
                unidadDocumentalId,
                "autor.prueba",
                List.of(new EstructuraMetadatoDocumento("folios", "10", TipoMetadatoEnum.CONTENIDO)),
                null, // Sin firmas
                null, // Sin aprobaciones
                List.of(new SolicitudRegistroDocumento.RegistroAnexo(
                        "anexo1",
                        "Descripción del anexo",
                        UUID.randomUUID().toString(),
                        "999b5233407e71fb28c80c218006341179cd67a83b64a56c0ac25064e094c441",
                        1000L,
                        "pdf"
                ))
        );
    }
}
