package co.com.gedsys.base.domain.documento;

import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

class AnexoTest {

    @Test
    void deberiaLanzarErrorCuandoFaltanCamposObligatorios() {
        final var HASH256 = "999b5233407e71fb28c80c218006341179cd67a83b64a56c0ac25064e094c441";
        Anexo.AnexoBuilder anexo = Anexo.builder();
        assertThrows(IllegalArgumentException.class, anexo::build);
        anexo.fileId(UUID.randomUUID().toString());
        assertThrows(IllegalArgumentException.class, anexo::build);
        anexo.hash(HASH256);
        assertThrows(IllegalArgumentException.class, anexo::build);
        anexo.extension("pdf");
        assertThrows(IllegalArgumentException.class, anexo::build);
        anexo.bytes(100L);
        assertThrows(IllegalArgumentException.class, anexo::build);
        anexo.nombre("Anexo");
//        assertDoesNotThrow(anexo::build);
        anexo.descripcion("Descripcion");
        assertDoesNotThrow(anexo::build);
    }

}