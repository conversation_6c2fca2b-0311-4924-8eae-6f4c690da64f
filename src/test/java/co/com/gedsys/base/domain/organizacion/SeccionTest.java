package co.com.gedsys.base.domain.organizacion;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

@DisplayName("Una Sección")
class SeccionTest {

    @Nested
    @DisplayName("al ser creada")
    class Creacion {
        @Test
        @DisplayName("debe inicializarse con estado ACTIVA")
        void deberiaInicializarseConEstadoActiva() {
            var seccion = new Seccion("01.00.00", "Despacho");
            assertThat(seccion.getEstado()).isEqualTo(EstadoSeccion.ACTIVA);
        }

        @Test
        @DisplayName("debe lanzar excepción si el código es nulo")
        void deberiaLanzarExcepcionSiCodigoEsNulo() {
            assertThrows(IllegalArgumentException.class,
                    () -> new Seccion(null, "Despacho"),
                    "Código y nombre son requeridos");
        }

        @Test
        @DisplayName("debe lanzar excepción si el nombre es nulo")
        void deberiaLanzarExcepcionSiNombreEsNulo() {
            assertThrows(IllegalArgumentException.class,
                    () -> new Seccion("01.00.00", null),
                    "Código y nombre son requeridos");
        }
    }


    private Seccion despacho;
    private Seccion secretaria;
    private Seccion auxiliar;
    private Seccion despachoSuperior;

    @BeforeEach
    void setUp() {
        despacho = new Seccion("02.00.00", "Despacho");
        secretaria = new Seccion("02.01.00", "Secretaria");
        auxiliar = new Seccion("02.01.01", "Auxiliar");
        despachoSuperior = new Seccion("01.00.00", "Despacho Superior");
    }

    @Nested
    @DisplayName("al establecer relaciones padre-hijo")
    class RelacionesPadreHijo {
        @Test
        @DisplayName("no debe permitir establecer una sección padre con categoría inferior al hijo")
        void deberiaValidarQueSeccionPadreNoPuedeTenerCategoriaMenorQueHijo() {
            // Assert
            assertThrows(IllegalArgumentException.class, () -> despacho.setPadre(secretaria));
            assertDoesNotThrow(() -> despacho.setPadre(despachoSuperior));
            assertThrows(IllegalArgumentException.class, () -> despacho.setPadre(auxiliar));
            assertDoesNotThrow(() -> auxiliar.setPadre(despacho));
        }

        @Test
        @DisplayName("no debe permitir que una sección se establezca a sí misma como padre")
        void deberiaValidarQueSeccionNoPuedeEstablecerseMismaComoPadre() {
            assertThrows(IllegalArgumentException.class, () -> despacho.setPadre(despacho));
        }

        @Test
        @DisplayName("no debe permitir establecer a un hijo como padre")
        void deberiaValidarQueSeccionNoPuedeEstablecerHijoComoPadre() {
            despachoSuperior.agregarHijo(despacho);
            assertThrows(IllegalArgumentException.class, () -> despachoSuperior.setPadre(despacho));
        }

        @Test
        @DisplayName("Deberia vincular al hijo cuando se asigna al padre")
        void deberiaVinlacarAlHijoCuandoSeAsignaAlPadre() {
            despacho.setPadre(despachoSuperior);
            assertThat(despachoSuperior.getHijos()).contains(despacho);
        }

        @Test
        @DisplayName("debe mantener la referencia bidireccional al establecer el padre")
        void deberiaManternerReferenciaBidireccionalAlEstablecerPadre() {
            despacho.setPadre(despachoSuperior);

            assertThat(despacho.getPadre()).isEqualTo(despachoSuperior);
            assertThat(despachoSuperior.getHijos()).contains(despacho);
        }

        @Test
        @DisplayName("debe eliminar correctamente la relación con el padre anterior")
        void deberiaEliminarRelacionConPadreAnterior() {
            // Configuración inicial
            despacho.setPadre(despachoSuperior);
            assertThat(despachoSuperior.getHijos()).contains(despacho);

            // Cambiar a un nuevo padre
            Seccion nuevoPadre = new Seccion("03.00.00", "Nuevo Despacho");
            despacho.setPadre(nuevoPadre);

            // Verificar que se eliminó del padre anterior y se agregó al nuevo
            assertThat(despachoSuperior.getHijos()).doesNotContain(despacho);
            assertThat(nuevoPadre.getHijos()).contains(despacho);
            assertThat(despacho.getPadre()).isEqualTo(nuevoPadre);
        }

        @Test
        @DisplayName("debe permitir establecer padre como null y eliminar la relación")
        void deberiaPermitirEstablecerPadreNullYEliminarRelacion() {
            // Configuración inicial
            despacho.setPadre(despachoSuperior);
            assertThat(despachoSuperior.getHijos()).contains(despacho);

            // Establecer padre como null
            despacho.setPadre(null);

            // Verificar que se eliminaron todas las referencias
            assertThat(despacho.getPadre()).isNull();
            assertThat(despachoSuperior.getHijos()).doesNotContain(despacho);
        }
    }

    @Nested
    @DisplayName("al gestionar hijos")
    class GestionHijos {
        @Test
        @DisplayName("debe permitir agregar un hijo válido")
        void deberiaValidarQueSeccionPuedeAgregarHijoValido() {
            despacho.agregarHijo(secretaria);
            assertThat(despacho.getHijos()).hasSize(1);
            assertThat(secretaria.getPadre()).isNotNull();
        }

        @Test
        @DisplayName("no debe permitir agregar al padre como hijo")
        void deberiaValidarQueSeccionNoPuedeAgregarPadreComoHijo() {
            // Arrange - Primero establecemos la relación hijo->padre
            despacho.setPadre(despachoSuperior);

            // Act & Assert - Intentamos agregar al padre como hijo
            assertThrows(IllegalArgumentException.class,
                    () -> despacho.agregarHijo(despachoSuperior),
                    "No debería permitir agregar como hijo a una sección que ya es padre");
        }

        @Test
        @DisplayName("no debe permitir agregar una sección de categoría superior como hijo")
        void deberiaValidarQueSeccionNoPuedeAgregarCategoriaSuperiorComoHijo() {
            assertThrows(IllegalArgumentException.class, () -> auxiliar.agregarHijo(secretaria));
            assertDoesNotThrow(() -> despacho.agregarHijo(secretaria));
        }

        @Test
        @DisplayName("no debe permitir que una sección se agregue a sí misma como hijo")
        void deberiaValidarQueSeccionNoPuedeAgregarseMismaComoHijo() {
            assertThrows(IllegalArgumentException.class, () -> despacho.agregarHijo(despacho));
        }

        @Test
        @DisplayName("no debe permitir agregar un hijo nulo")
        void deberiaValidarQueSeccionNoPuedeAgregarHijoNulo() {
            assertThrows(IllegalArgumentException.class, () -> despacho.agregarHijo(null));
        }

        @Test
        @DisplayName("debe ignorar cuando se intenta agregar un hijo que ya existe")
        void deberiaIgnorarCuandoSeIntentaAgregarHijoExistente() {
            // Arrange
            despacho.agregarHijo(secretaria);
            int cantidadHijosInicial = despacho.getHijos().size();
            Seccion padreOriginal = secretaria.getPadre();

            // Act
            despacho.agregarHijo(secretaria);

            // Assert
            assertThat(despacho.getHijos()).hasSize(cantidadHijosInicial);
            assertThat(secretaria.getPadre()).isEqualTo(padreOriginal);
            assertThat(despacho.getHijos()).contains(secretaria);
        }
    }

    @Nested
    @DisplayName("al remover hijos")
    class RemoverHijos {
        @BeforeEach
        void configurarHijos() {
            despacho.agregarHijo(secretaria);
        }

        @Test
        @DisplayName("debe permitir remover un hijo existente")
        void deberiaRemoverUnHijoDeUnaSeccion() {
            assertThat(despacho.getHijos()).hasSize(1);
            despacho.removerHijo(secretaria);
            assertThat(despacho.getHijos()).isEmpty();
        }

        @Test
        @DisplayName("debe lanzar error al intentar remover un hijo que no existe")
        void deberiaLanzarErrorCuandoSeIntentaRemoverUnHijoQueNoExiste() {
            var secretariaInexistente = new Seccion("01.01.01", "Secretaria");
            assertThrows(IllegalArgumentException.class, () -> despacho.removerHijo(secretariaInexistente));
        }
    }

    @Nested
    @DisplayName("al validar jerarquía por código")
    class ValidacionJerarquiaCodigo {
        @Test
        @DisplayName("debe permitir asignar hijo con código jerárquicamente válido")
        void deberiaPermitirAsignarHijoConCodigoJerarquicoValido() {
            // Arrange
            var padre = new Seccion("02.00.00", "Despacho");
            var hijo = new Seccion("02.01.00", "Secretaría");
            var nieto = new Seccion("02.01.01", "Auxiliar");

            // Act & Assert
            assertDoesNotThrow(() -> padre.agregarHijo(hijo));
            assertDoesNotThrow(() -> hijo.agregarHijo(nieto));
        }

        @Test
        @DisplayName("debe rechazar hijo con código jerárquicamente inválido")
        void deberiaRechazarHijoConCodigoJerarquicoInvalido() {
            // Arrange
            var despacho = new Seccion("02.00.00", "Despacho");
            var secretariaInvalida = new Seccion("03.01.00", "Secretaría");

            // Primero establecemos un padre para el despacho para activar la validación
            var nodoPrincipal = new Seccion("01.00.00", "Nodo Principal");
            despacho.setPadre(nodoPrincipal);

            // Act & Assert
            assertThrows(IllegalArgumentException.class,
                    () -> despacho.agregarHijo(secretariaInvalida),
                    "El código del hijo debe pertenecer a la misma jerarquía del padre");

            assertThrows(IllegalArgumentException.class,
                    () -> secretariaInvalida.setPadre(despacho),
                    "El código del hijo debe pertenecer a la misma jerarquía del padre");
        }

        @Test
        @DisplayName("debe permitir cualquier código cuando el padre es nodo principal")
        void deberiaPermitirCualquierCodigoCuandoPadreEsNodoPrincipal() {
            // Arrange
            var padreNodoPrincipal = new Seccion("01.00.00", "Despacho Principal");
            var hijoOtroDespacho = new Seccion("02.01.00", "Secretaría Otro Despacho");

            // Act & Assert
            assertDoesNotThrow(() -> padreNodoPrincipal.agregarHijo(hijoOtroDespacho));
        }
    }

    @Nested
    @DisplayName("al consultar información")
    class Consultas {
        private Seccion seccion;
        private Seccion padre;

        @BeforeEach
        void setUp() {
            padre = new Seccion("01.00.00", "Despacho");
            seccion = new Seccion("01.01.00", "Secretaría");
            seccion.setPadre(padre);
        }

        @Test
        @DisplayName("debe retornar el código del superior inmediato")
        void deberiaRetornarCodigoSuperiorInmediato() {
            assertThat(seccion.codigoSuperiorInmediato()).isEqualTo("01.00.00");
            assertThat(padre.codigoSuperiorInmediato()).isNull();
        }

        @Test
        @DisplayName("debe retornar el nombre del superior inmediato")
        void deberiaRetornarNombreSuperiorInmediato() {
            assertThat(seccion.nombreSuperiorInmediato()).isEqualTo("Despacho");
            assertThat(padre.nombreSuperiorInmediato()).isNull();
        }

        @Test
        @DisplayName("debe identificar correctamente si es un despacho")
        void deberiaIdentificarSiEsDespacho() {
            assertThat(padre.esUnDespacho()).isTrue();
            assertThat(seccion.esUnDespacho()).isFalse();
        }
    }

    @Nested
    @DisplayName("al gestionar usuarios")
    class GestionUsuarios {
        private Seccion seccion;
        private String username;
        private TipoRelacionUsuarioSeccion relacion;

        @BeforeEach
        void setUp() {
            seccion = new Seccion("01.00.00", "Despacho");
            username = "usuario1";
            relacion = TipoRelacionUsuarioSeccion.PRIMARIA;
        }

        @Test
        @DisplayName("debe permitir agregar un usuario válido")
        void deberiaPermitirAgregarUsuarioValido() {
            // Act
            seccion.agregarUsuario(username, relacion);

            // Assert
            assertThat(seccion.getUsuarios()).hasSize(1);
            assertThat(seccion.getUsuarios().stream()
                    .anyMatch(u -> u.getUsername().equals(username) && u.getRelacion() == relacion))
                    .isTrue();
        }

        @Test
        @DisplayName("debe lanzar excepción al agregar un usuario con username nulo")
        void deberiaLanzarExcepcionAlAgregarUsuarioNulo() {
            assertThrows(IllegalArgumentException.class, 
                    () -> seccion.agregarUsuario(null, relacion),
                    "El username y la relación no pueden ser nulos");
        }

        @Test
        @DisplayName("debe lanzar excepción al agregar un usuario con relación nula")
        void deberiaLanzarExcepcionAlAgregarUsuarioConRelacionNula() {
            assertThrows(IllegalArgumentException.class, 
                    () -> seccion.agregarUsuario(username, null),
                    "El username y la relación no pueden ser nulos");
        }

        @Test
        @DisplayName("debe lanzar excepción al agregar un usuario duplicado")
        void deberiaLanzarExcepcionAlAgregarUsuarioDuplicado() {
            // Arrange
            seccion.agregarUsuario(username, relacion);

            // Act & Assert
            assertThrows(UsuarioSeccionDuplicadoException.class, 
                    () -> seccion.agregarUsuario(username, relacion));
        }

        @Test
        @DisplayName("debe lanzar excepción al agregar un usuario con relación diferente")
        void deberiaLanzarExcepcionAlAgregarUsuarioConRelacionDiferente() {
            // Arrange
            seccion.agregarUsuario(username, relacion);
            TipoRelacionUsuarioSeccion relacionDiferente = TipoRelacionUsuarioSeccion.SECUNDARIA;

            // Act & Assert
            assertThrows(UsuarioSeccionRelacionDobleException.class,
                    () -> seccion.agregarUsuario(username, relacionDiferente));
        }

        @Test
        @DisplayName("debe permitir remover un usuario existente")
        void deberiaPermitirRemoverUsuarioExistente() {
            // Arrange
            seccion.agregarUsuario(username, relacion);
            assertThat(seccion.getUsuarios()).hasSize(1);

            // Act
            seccion.removerUsuario(username);

            // Assert
            assertThat(seccion.getUsuarios()).isEmpty();
        }

        @Test
        @DisplayName("debe lanzar excepción al remover un usuario que no existe")
        void deberiaLanzarExcepcionAlRemoverUsuarioInexistente() {
            assertThrows(IllegalArgumentException.class, 
                    () -> seccion.removerUsuario("usuarioInexistente"),
                    "El usuario no existe en la sección");
        }

        @Test
        @DisplayName("debe lanzar excepción al remover un usuario con username nulo")
        void deberiaLanzarExcepcionAlRemoverUsuarioConUsernameNulo() {
            assertThrows(IllegalArgumentException.class, 
                    () -> seccion.removerUsuario(null),
                    "El username no puede ser nulo");
        }
    }
}
