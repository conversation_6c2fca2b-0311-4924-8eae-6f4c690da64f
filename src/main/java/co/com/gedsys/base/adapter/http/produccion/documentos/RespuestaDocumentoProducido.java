package co.com.gedsys.base.adapter.http.produccion.documentos;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public record RespuestaDocumentoProducido(
        UUID id,
        String titulo,
        String autor,
        String codigoClasificatorio,
        String unidadDocumentalNombre,
        String unidadDocumentalId,
        String tipoDocumentalNombre,
        String tipoDocumentalId,
        List<MetadatoDS> metadatos,
        List<FirmaDS> firmas,
        List<AprobacionDS> aprobaciones,
        List<AnexoDS> anexos) {
    public record MetadatoDS(String id, String patron, String nombre, String tipo, String formato,
                             String valor) {
    }

    public record FirmaDS(
            UUID id,
            String owner,
            String estado,
            LocalDateTime firmadoEn,
            int height,
            String observaciones,
            int page,
            int width,
            int x,
            int y
    ) {
    }

    public record AprobacionDS(
            UUID id,
            String aprobador,
            String observaciones,
            LocalDateTime aprobadoEn,
            String estado
    ) {
    }

    public record AnexoDS(
            UUID id,
            String nombre,
            String descripcion,
            String fileId,
            String hash,
            Long bytes,
            String extension
    ) {
    }
}
