package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.infrastructure.data_access.mappers.SeccionEntityMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.SeccionJpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public class SeccionGateway implements SeccionRepository {
    private final SeccionJpaRepository jpaRepository;
    private final SeccionEntityMapper seccionEntityMapper;

    public SeccionGateway(SeccionJpaRepository jpaRepository,
                          SeccionEntityMapper seccionEntityMapper) {
        this.jpaRepository = jpaRepository;
        this.seccionEntityMapper = seccionEntityMapper;
    }

    @Override
    public Optional<Seccion> findByCode(String codigo) {
        return jpaRepository.findByCodigo(codigo)
                .map(seccionEntityMapper::toDomainEntity);
    }

    @Override
    public List<Seccion> buscarSeccionesPorEstado(EstadoSeccion estado) {
        return jpaRepository.findByEstado(estado)
                .stream()
                .map(seccionEntityMapper::toDomainEntity)
                .toList();
    }

    @Override
    public Seccion save(Seccion domainEntity) {
        var entity = seccionEntityMapper.toEntity(domainEntity);
        return seccionEntityMapper.toDomainEntity(jpaRepository.save(entity));
    }

    @Override
    public Optional<Seccion> findById(UUID id) {
        return jpaRepository.findById(id)
                .map(seccionEntityMapper::toDomainEntity);
    }

    @Override
    public Optional<Seccion> buscarNodoPrincipalDelOrganigrama() {
        return jpaRepository.findByPadreIsNullAndEstadoIs(EstadoSeccion.ACTIVA)
                .map(seccionEntityMapper::toDomainEntity);
    }

    @Override
    public List<Seccion> findAll() {
        return jpaRepository.findAll()
                .stream()
                .map(seccionEntityMapper::toDomainEntity)
                .toList();
    }

    @Override
    public boolean checkStock(String name) {
        return false;
    }

    @Override
    public boolean checkStock(Seccion entity) {
        return false;
    }

    @Override
    public void delete(UUID uuid) {
        jpaRepository.deleteById(uuid);
    }

    @Override
    public boolean tieneHijos(UUID id) {
        return jpaRepository.existsByPadre_Id(id);
    }

    @Override
    public List<Seccion> buscarSeccionesPorUsuario(String username) {
        return jpaRepository.findByUsuarios_Username(username)
                .stream()
                .map(seccionEntityMapper::toDomainEntity)
                .toList();
    }

    @Override
    public boolean tieneUsuarios(UUID id) {
        return jpaRepository.existsByUsuarios_Seccion_Id(id);
    }
}
