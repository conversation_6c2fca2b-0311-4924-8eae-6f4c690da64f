package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.infrastructure.data_access.SerieDocumentalEntity;
import co.com.gedsys.base.adapter.persistence.mappers.SerieDocumentalPersistenceMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.SerieDocumentalJpaRepository;
import co.com.gedsys.base.domain.serie_documental.EstadoSerie;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class SerieDocumentalGateway implements SerieDocumentalRepository {
    private final SerieDocumentalJpaRepository jpaRepository;
    private final SerieDocumentalPersistenceMapper mapper;

    @Override
    public boolean existenciaPorCodigo(String codigo) {
        var example = SerieDocumentalEntity.builder().codigo(codigo).build();
        return jpaRepository.exists(Example.of(example));
    }

    @Override
    public void guardar(SerieDocumental serieDocumental) {
        var entity = mapper.toEntity(serieDocumental);
        jpaRepository.save(entity);
    }

    @Override
    public Optional<SerieDocumental> findByCode(String codigo) {
        return jpaRepository.findByCodigo(codigo)
                .map(mapper::toDomain);
    }

    @Override
    public List<SerieDocumental> buscar(EstadoSerie estado) {
        ExampleMatcher matcher = ExampleMatcher.matchingAll();
        Example<SerieDocumentalEntity> example = Example.of(SerieDocumentalEntity.builder().estado(estado).build(), matcher);
        return jpaRepository.findAll(example).stream().map(mapper::toDomain).toList();
    }
}
