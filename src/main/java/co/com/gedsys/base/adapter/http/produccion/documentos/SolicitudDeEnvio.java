package co.com.gedsys.base.adapter.http.produccion.documentos;

import java.util.List;
import java.util.UUID;

import jakarta.validation.constraints.NotNull;

public record SolicitudDeEnvio(
        @NotNull(message = "El destinatario es requerido")
        UUID destinatarioId,
        List<EstructuraMetadatoDocumento> metadatos,
        @NotNull(message = "Las propiedades del radicado son requeridas")
        EstructuraPropiedadesRadicado propiedadesRadicado) {
}
