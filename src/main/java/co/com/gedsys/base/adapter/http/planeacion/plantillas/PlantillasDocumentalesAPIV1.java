package co.com.gedsys.base.adapter.http.planeacion.plantillas;

import co.com.gedsys.base.domain.plantillas.TipoPlantilla;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.UUID;

// DTOs para la API
import co.com.gedsys.base.adapter.http.planeacion.plantillas.PlantillaItemDS;
import co.com.gedsys.base.adapter.http.planeacion.plantillas.PlantillaDetalladaDS;
import co.com.gedsys.base.adapter.http.planeacion.plantillas.ActualizarPlantillaRequest;

public interface PlantillasDocumentalesAPIV1 {

    @PostMapping(path = "", consumes = "application/json", produces = "application/json")
    ResponseEntity<Void> create(
            @RequestBody @Valid PlantillaCreateRequest request,
            UriComponentsBuilder uriBuilder
    );

    @GetMapping(path = "", produces = "application/json")
    List<PlantillaItemDS> getAll(@RequestParam(required = false, defaultValue = "PRODUCCION") TipoPlantilla tipo);

    @GetMapping(path = "/{id}", produces = "application/json")
    ResponseEntity<PlantillaDetalladaDS> get(@PathVariable UUID id);

    @PatchMapping(path = "/{id}", consumes = "application/json", produces = "application/json")
    ResponseEntity<Void> update(@PathVariable UUID id, @RequestBody ActualizarPlantillaRequest request);

    @PostMapping(path = "/{plantillaId}/activar")
    ResponseEntity<Void> activate(@PathVariable UUID plantillaId);


    @PostMapping(path = "/{plantillaId}/desactivar")
    ResponseEntity<Void> deactivate(@PathVariable UUID plantillaId);

    @GetMapping(path = "/by-tipo-documental/permitidos", produces = "application/json", params = {"tipoDocumental"})
    List<PlantillaItemDS> porTiposDocumentalesPermitidos(@RequestParam("tipoDocumental") List<String> tiposDocumentales);
}
