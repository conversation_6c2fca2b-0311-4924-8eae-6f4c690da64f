package co.com.gedsys.base.adapter.http.produccion.documentos;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.UUID;

public record SolicitudRegistroDocumento(
        @NotBlank(message = "El título del documento no puede estar vacío")
        String titulo,
        @NotBlank(message = "La referencia al archivo debe ser requerida")
        String fileId,
        @NotNull(message = "El tipo de documento no puede estar vacío")
        UUID tipoDocumentalId,
        @NotNull(message = "Debe especificar la unidad documental")
        UUID unidadDocumentalId,
        @NotBlank(message = "El autor no puede estar vacío")
        String autor,
        @Valid
        List<EstructuraMetadatoDocumento> metadatos,
        @Valid
        List<FirmaUsuario> firmas,
        @Valid
        List<Aprobacion> aprobaciones,
        @Valid
        List<RegistroAnexo> anexos) {
    public record FirmaUsuario(
            @NotBlank(message = "El propietario de la firma no puede estar vacío")
            String owner,
            @Min(value = 0, message = "La altura no puede ser negativa")
            int height,
            String observaciones,
            @Min(value = 0, message = "La página no puede ser negativa")
            int page,
            @Min(value = 0, message = "El ancho no puede ser negativo")
            int width,
            @Min(value = 0, message = "La coordenada x no puede ser negativa")
            int x,
            @Min(value = 0, message = "La coordenada y no puede ser negativa")
            int y
    ) {
    }

    public record Aprobacion(
            @NotBlank(message = "El aprobador no puede estar vacío")
            String aprobador
    ) {
    }

    public record RegistroAnexo(
            @NotBlank(message = "El nombre del anexo no puede estar vacío")
            String nombre,
            String descripcion,
            @NotBlank(message = "El archivo del anexo no puede estar vacío")
            String fileId,
            @NotBlank(message = "El hash del anexo no puede estar vacío")
            String hash,
            @Min(value = 1, message = "El tamaño del anexo no puede ser negativo")
            Long bytes,
            @NotBlank(message = "La extensión del anexo no puede estar vacío")
            String extension
    ) {
    }
}
