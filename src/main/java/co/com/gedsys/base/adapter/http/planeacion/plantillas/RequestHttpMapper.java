package co.com.gedsys.base.adapter.http.planeacion.plantillas;

import co.com.gedsys.base.application.usecase.planeacion.plantillas.ActualizarPlantillaCommand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR
)
interface RequestHttpMapper {


    @Mapping(target = "plantillaId", ignore = true)
    @Mapping(target = "tipoDocumentalId", source = "tipoDocumental")
    ActualizarPlantillaCommand toCommand(ActualizarPlantillaRequest request);
}
