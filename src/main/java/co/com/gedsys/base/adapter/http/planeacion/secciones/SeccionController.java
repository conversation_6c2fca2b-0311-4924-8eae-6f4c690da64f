package co.com.gedsys.base.adapter.http.planeacion.secciones;

import co.com.gedsys.base.application.usecase.planeacion.seccion.*;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import jakarta.validation.Valid;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
public class SeccionController implements SeccionAPI {

    private final AgregarSeccionUseCase agregarSeccionUseCase;
    private final EliminarSeccionUseCase eliminarSeccionUseCase;
    private final ActualizarSeccionUseCase actualizarSeccionUseCase;
    private final AgregarUsuarioSeccionUseCase agregarUsuarioUseCase;
    private final EliminarUsuarioSeccionUseCase eliminarUsuarioUseCase;
    private final MoverSeccionUseCase moverSeccionUseCase;
    private final BuscarUsuariosSeccionUseCase buscarUsuariosSeccionUseCase;
    private final BuscarSeccionesDeUsuarioUseCase buscarSeccionesDeUsuarioUseCase;
    private final ListarSeccionesUseCase listarSeccionesUseCase;
    private final SeccionControllerMapper mapper;

    public SeccionController(AgregarSeccionUseCase agregarSeccionUseCase,
                             EliminarSeccionUseCase eliminarSeccionUseCase,
                             ActualizarSeccionUseCase actualizarSeccionUseCase,
                             AgregarUsuarioSeccionUseCase agregarUsuarioUseCase,
                             EliminarUsuarioSeccionUseCase eliminarUsuarioUseCase,
                             MoverSeccionUseCase moverSeccionUseCase,
                             BuscarUsuariosSeccionUseCase buscarUsuariosSeccionUseCase, BuscarSeccionesDeUsuarioUseCase buscarSeccionesDeUsuarioUseCase,
                             ListarSeccionesUseCase listarSeccionesUseCase,
                             SeccionControllerMapper mapper) {
        this.agregarSeccionUseCase = agregarSeccionUseCase;
        this.eliminarSeccionUseCase = eliminarSeccionUseCase;
        this.actualizarSeccionUseCase = actualizarSeccionUseCase;
        this.agregarUsuarioUseCase = agregarUsuarioUseCase;
        this.eliminarUsuarioUseCase = eliminarUsuarioUseCase;
        this.moverSeccionUseCase = moverSeccionUseCase;
        this.buscarUsuariosSeccionUseCase = buscarUsuariosSeccionUseCase;
        this.buscarSeccionesDeUsuarioUseCase = buscarSeccionesDeUsuarioUseCase;
        this.listarSeccionesUseCase = listarSeccionesUseCase;
        this.mapper = mapper;
    }

    @CacheEvict(value = "secciones", allEntries = true)
    @Override
    public ResponseEntity<SeccionPlaneacionDS> create(UnidadProductoraRequest request) {
        var command = mapper.toCommand(request);
        final var result = agregarSeccionUseCase.execute(command);
        return ResponseEntity.ok(mapper.toResponse(result));
    }

    @Override
    public ResponseEntity<List<SeccionPlaneacionDS>> listar(String nombre, EstadoSeccionPlaneacion estado) {
        EstadoSeccion estadoDominio = estado != null ? EstadoSeccion.valueOf(estado.name()) : null;
        var command = new ListarSeccionesCommand(nombre, estadoDominio);
        var result = listarSeccionesUseCase.execute(command);
        return ResponseEntity.ok(result.stream()
                .map(mapper::toResponse)
                .toList());
    }

    @Override
    public ResponseEntity<Void> delete(UUID id) {
        var command = new EliminarSeccionCommand(id);
        eliminarSeccionUseCase.execute(command);
        return ResponseEntity.noContent().build();
    }

    @CacheEvict(value = "secciones", allEntries = true)
    @Override
    public ResponseEntity<SeccionPlaneacionDS> update(UUID id, ActualizarSeccionRequest request) {
        var command = mapper.toCommand(id, request);
        var resultado = actualizarSeccionUseCase.execute(command);
        return ResponseEntity.ok(mapper.toResponse(resultado));
    }

    @CacheEvict(value = "secciones", allEntries = true)
    @Override
    public ResponseEntity<Void> move(UUID id, MoverSeccionRequest request) {
        var command = new MoverSeccionCommand(id, request.nuevoPadreId(), request.nuevoCodigo());
        moverSeccionUseCase.execute(command);
        return ResponseEntity.noContent().build();
    }

    @CacheEvict(value = "secciones", allEntries = true)
    @Override
    public ResponseEntity<Void> agregarUsuario(UUID seccionId, @Valid AgregarUsuarioRequest request) {
        var command = new AgregarUsuarioSeccionCommand(seccionId, request.username(), request.tipoRelacion());
        agregarUsuarioUseCase.execute(command);
        return ResponseEntity.noContent().build();
    }

    @CacheEvict(value = "secciones", allEntries = true)
    @Override
    public ResponseEntity<Void> removerUsuario(UUID seccionId, String username) {
        var command = new EliminarUsuarioSeccionCommand(seccionId, username);
        eliminarUsuarioUseCase.execute(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<List<UsuarioSeccionDS>> buscarUsuarios(UUID seccionId) {
        var command = new BuscarUsuariosSeccionCommand(seccionId);
        var result = buscarUsuariosSeccionUseCase.execute(command);
        return ResponseEntity.ok(result.stream()
                .map(mapper::toResponse)
                .toList());
    }

    @Override
    public ResponseEntity<List<SeccionPlaneacionDS>> recuperarLasSeccionesDeUnUsuario(String username) {
        return ResponseEntity.ok(buscarSeccionesDeUsuarioUseCase.execute(username).stream()
                .map(mapper::toResponse).toList());
    }
}
