package co.com.gedsys.base.adapter.http.planeacion.series_documentales;

import co.com.gedsys.base.application.usecase.planeacion.series_documentales.BuscarSeriesQuery;
import co.com.gedsys.base.application.usecase.planeacion.series_documentales.BuscarSeriesUseCase;
import co.com.gedsys.base.application.usecase.planeacion.series_documentales.RegistrarSerieDocumentalUseCase;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static co.com.gedsys.base.domain.serie_documental.EstadoSerie.ACTIVA;

@RequiredArgsConstructor
@RestController
@RequestMapping(path = "/api/v1/planeacion/series-documentales")
public class SerieDocumentalController implements SerieDocumentalApi {

    private final RegistrarSerieDocumentalUseCase registrarSerieUseCase;
    private final BuscarSeriesUseCase buscarSeriesUseCase;
    private final SerieDocumentalPlaneacionMapper mapper;

    @Override
    public ResponseEntity<Void> create(RegistrarSerieDocumentalRequest request) {
        var command = mapper.toCommand(request);
        registrarSerieUseCase.execute(command);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<List<SerieDocumentalItem>> getList() {
        var query = new BuscarSeriesQuery(ACTIVA);
        var result = buscarSeriesUseCase.execute(query).stream()
                .map(mapper::toItem)
                .toList();
        return ResponseEntity.ok(result);
    }
}
