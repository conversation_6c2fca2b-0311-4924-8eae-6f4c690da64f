package co.com.gedsys.base.adapter.persistence;


import co.com.gedsys.base.adapter.persistence.mappers.DataAccessConsecutivoMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.ConsecutivoJpaRepository;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class ConsecutivoGateway implements ConsecutivoRepository {

    private final ConsecutivoJpaRepository jpaRepository;
    private final DataAccessConsecutivoMapper map;

    @Override
    public Consecutivo save(Consecutivo consecutivo) {
        var entity = map.toEntity(consecutivo);
        return map.toDomain(jpaRepository.save(entity));
    }

    @Override
    public Optional<Consecutivo> findById(UUID id) {
        return jpaRepository.findById(id)
                .map(map::toDomain);
    }

    @Override
    public List<Consecutivo> findAll() {
        return jpaRepository.findAll()
                .stream()
                .map(map::toDomain)
                .toList();
    }

    @Override
    public boolean checkStock(String name) {
        throw new UnsupportedOperationException("Method not implemented");
    }

    @Override
    public boolean checkStock(Consecutivo entity) {
        var example = map.toEntity(entity);

        return false;
    }

    @Override
    public void delete(UUID uuid) {

    }

    @Override
    public Optional<Consecutivo> findByExample(Consecutivo sample) {
        var example = map.toEntity(sample);
        var matcher = ExampleMatcher.matching()
                .withIgnorePaths("id", "estado", "contador", "prefijo", "sufijo")
                .withMatcher("tipoConsecutivo", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("clasificacionDocumentalId", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("tipoDocumentalId", ExampleMatcher.GenericPropertyMatchers.exact());

        return jpaRepository.findOne(Example.of(example, matcher))
                .map(map::toDomain);
    }

    @Override
    public List<Consecutivo> buscarConsecutivosParaTipoDocumental(TipoDocumental tipoDocumental) {
        return jpaRepository.searchAllByTipoDocumental_Id(tipoDocumental.getId())
                .stream()
                .map(map::toDomain)
                .toList();
    }
}
