package co.com.gedsys.base.adapter.http.planeacion.secciones;

import java.util.List;
import java.util.UUID;

import co.com.gedsys.commons.constant.CustomHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RequestMapping(path = "/api/v1/planeacion/secciones")
public interface SeccionAPI {

        @PostMapping(consumes = "application/json")
        ResponseEntity<SeccionPlaneacionDS> create(@RequestBody @Valid UnidadProductoraRequest request);


        @GetMapping(path = "", produces = "application/json")
        ResponseEntity<List<SeccionPlaneacionDS>> listar(
                @RequestParam(required = false) String nombre,
                @RequestParam(required = false) EstadoSeccionPlaneacion estado);

        @DeleteMapping(path = "/{id}")
        ResponseEntity<Void> delete(@PathVariable UUID id);

        @PatchMapping(path = "/{id}", consumes = "application/json", produces = "application/json")
        ResponseEntity<SeccionPlaneacionDS> update(
                        @PathVariable UUID id,
                        @RequestBody @Valid ActualizarSeccionRequest request);

        @PatchMapping(path = "/{id}/mover", consumes = "application/json")
        ResponseEntity<Void> move(
                        @PathVariable UUID id,
                        @RequestBody @Valid MoverSeccionRequest request);

        @PostMapping(path = "/{seccionId}/usuarios", consumes = "application/json")
        ResponseEntity<Void> agregarUsuario(@PathVariable UUID seccionId, @RequestBody @Valid AgregarUsuarioRequest request);

        @DeleteMapping(path = "/{seccionId}/usuarios/{username}")
        ResponseEntity<Void> removerUsuario(@PathVariable UUID seccionId, @PathVariable String username);

        @GetMapping(path = "/{seccionId}/usuarios", produces = "application/json")
        ResponseEntity<List<UsuarioSeccionDS>> buscarUsuarios(@PathVariable UUID seccionId);

        @GetMapping(path = "secciones-usuario")
        ResponseEntity<List<SeccionPlaneacionDS>> recuperarLasSeccionesDeUnUsuario(@RequestHeader(CustomHeaders.USERNAME) String username);
}
