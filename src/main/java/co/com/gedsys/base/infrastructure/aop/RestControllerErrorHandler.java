package co.com.gedsys.base.infrastructure.aop;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestControllerAdvice
public class RestControllerErrorHandler {

    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ErrorResponse> handleEntityAlreadyExistsException(RuntimeException ex) {
        var errorResponse = new ErrorResponse(
                HttpStatus.CONFLICT.value(),
                ex.getClass().getSimpleName(),
                ex.getMessage()
        );
        errorLocation(ex);
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgumentException(IllegalArgumentException ex) {
        var errorResponse = new ErrorResponse(
                HttpStatus.BAD_REQUEST.value(),
                ex.getClass().getSimpleName(),
                ex.getMessage()
        );
        errorLocation(ex);
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(EntityNotExistsException.class)
    public ResponseEntity<ErrorResponse> handleEntityNotExistsException(EntityNotExistsException ex) {
        var errorResponse = new ErrorResponse(
                HttpStatus.NOT_FOUND.value(),
                ex.getClass().getSimpleName(),
                ex.getMessage()
        );
        errorLocation(ex);
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationExceptions(MethodArgumentNotValidException ex) {
        List<String> errors = new ArrayList<>();

        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            errors.add(error.getField() + ": " + error.getDefaultMessage());
        }

        var errorsResponse = new ErrorResponse(
                HttpStatus.BAD_REQUEST.value(),
                ex.getClass().getSimpleName(),
                String.join(", ", errors)
        );
        errorLocation(ex);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorsResponse);
    }


    private void errorLocation(Exception ex) {
        StackTraceElement[] stackTrace = ex.getStackTrace();
        String errorLocation = "";

        if (stackTrace.length > 0) {
            StackTraceElement element = stackTrace[0];
            errorLocation = String.format("Error in class %s, line %d, method %s",
                    element.getClassName(),
                    element.getLineNumber(),
                    element.getMethodName());
        }
        log.error(errorLocation, ex);
    }


    public record ErrorResponse(int status, String error, String message) {

    }
}