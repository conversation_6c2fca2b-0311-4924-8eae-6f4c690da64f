package co.com.gedsys.base.infrastructure.data_access;

import jakarta.persistence.*;
import lombok.*;

import java.util.UUID;

@Entity
@Table(name = "anexos")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class AnexoEntity {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "nombre", nullable = false)
    private String nombre;

    @Column(name = "descripcion")
    private String descripcion;

    @Column(name = "file_id", nullable = false)
    private String fileId;

    @Column(name = "hash", nullable = false)
    private String hash;

    @Column(name = "bytes", nullable = false)
    private Long bytes;

    @Column(name = "extension", nullable = false)
    private String extension;

    @ManyToOne
    @JoinColumn(name = "documento_id", nullable = false)
    private DocumentoEntity documento;
}
