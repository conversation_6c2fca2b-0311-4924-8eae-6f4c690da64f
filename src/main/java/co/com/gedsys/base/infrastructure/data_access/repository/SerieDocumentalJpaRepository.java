package co.com.gedsys.base.infrastructure.data_access.repository;

import co.com.gedsys.base.infrastructure.data_access.SerieDocumentalEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;
import java.util.UUID;

public interface SerieDocumentalJpaRepository extends JpaRepository<SerieDocumentalEntity, UUID> {
    Optional<SerieDocumentalEntity> findByCodigo(String codigo);
}