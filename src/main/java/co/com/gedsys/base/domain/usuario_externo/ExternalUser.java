package co.com.gedsys.base.domain.usuario_externo;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.UUID;


@Getter @Setter @Accessors(chain = true)
public class ExternalUser {
    private UUID id;
    private String notes;
    private String salutation;

    private ExternalUserStatus status;
    private ExternalUserIdentificationType identificationType;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private ExternalUserName name;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private IdentificationNumber identificationNumber;

    private List<ExternalUserProperty> properties;

    public ExternalUser(String name, String identificationType, String identificationNumber) {
        this.name = new ExternalUserName(name);
        this.identificationType = ExternalUserIdentificationType.valueOf(identificationType);
        this.identificationNumber = new IdentificationNumber(identificationNumber);
        this.status = ExternalUserStatus.ACTIVO;
    }

    public String getIdentificationNumber() {
        return identificationNumber.getValue();
    }

    public ExternalUser setIdentificationNumber(String identificationNumber) {
        this.identificationNumber = new IdentificationNumber(identificationNumber);
        return this;
    }

    public String getName() {
        return name.getValue();
    }

    public ExternalUser setName(String name) {
        this.name = new ExternalUserName(name);
        return this;
    }

    public ExternalUser addProperties(List<ExternalUserProperty> properties) {
        properties.forEach(p -> p.setOwner(this));
        this.properties.addAll(properties);
        return this;
    }
}
