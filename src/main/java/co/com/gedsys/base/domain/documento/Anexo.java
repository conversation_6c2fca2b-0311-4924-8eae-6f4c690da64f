package co.com.gedsys.base.domain.documento;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter(AccessLevel.PACKAGE)
public class Anexo {
    private UUID id;
    private NombreAnexo nombre;
    private String descripcion;
    private AnexoID fileId;
    private Sha256 hash;
    private AttachmentByteSize bytes;
    private ExtensionAnexo extension;

    @Builder
    public Anexo(
            UUID id,
            String nombre,
            String descripcion,
            String fileId,
            String hash,
            Long bytes,
            String extension
    ) {
        this.id = id;
        this.nombre = new NombreAnexo(nombre);
        this.descripcion = descripcion;
        this.fileId = new AnexoID(fileId);
        this.hash = new Sha256(hash);
        this.bytes = new AttachmentByteSize(bytes);
        this.extension = new ExtensionAnexo(extension);
    }

    public String getExtension() {
        return extension.getValue();
    }

    public void setExtension(String extension) {
        this.extension = new ExtensionAnexo(extension);
    }

    public String getNombre() {
        return nombre.getValue();
    }

    public void setNombre(String nombre) {
        this.nombre = new NombreAnexo(nombre);
    }

    public String getHash() {
        return hash.getValue();
    }

    public void setHash(String hash) {
        this.hash = new Sha256(hash);
    }

    public String getFileId() {
        return fileId.getValue();
    }

    public void setFileId(String fileId) {
        this.fileId = new AnexoID(fileId);
    }

    public Long getBytes() {
        return bytes.getValue();
    }

    public void setBytes(Long bytes) {
        this.bytes = new AttachmentByteSize(bytes);
    }
}