package co.com.gedsys.base.domain.consecutivo;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import co.com.gedsys.base.domain.common.DomainRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;

public interface ConsecutivoRepository extends DomainRepository<Consecutivo, UUID> {

    Optional<Consecutivo> findByExample(Consecutivo sample);

    List<Consecutivo> buscarConsecutivosParaTipoDocumental(TipoDocumental tipoDocumental);
}
