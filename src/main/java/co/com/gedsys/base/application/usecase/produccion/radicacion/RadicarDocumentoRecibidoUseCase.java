package co.com.gedsys.base.application.usecase.produccion.radicacion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.AnexoDocumentoDTO;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.PropiedadRadicadoDTO;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoRecibidoCommand;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.documento.Anexo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.metadato.Metadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.radicado.PropiedadesRadicado;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RadicarDocumentoRecibidoUseCase implements UseCase<RadicarDocumentoRecibidoCommand, RadicadoDTO> {

    private final DocumentoRepository documentoRepository;
    private final TipoDocumentalRepository tipoDocumentalRepository;
    private final UnidadDocumentalRepository unidadDocumentalRepository;
    private final DefinicionMetadatosRepository definicionMetadatosRepository;
    private final ConsecutivoRepository consecutivoRepository;
    private final RadicadoRepository radicadoRepository;
    private final RadicadoApplicationLayerMapper radicadoMapper;

    @Override
    @Transactional
    public RadicadoDTO execute(RadicarDocumentoRecibidoCommand input) {
        Documento documento = crearDocumento(input);
        Documento documentoGuardado = documentoRepository.save(documento);

        Consecutivo consecutivo = obtenerConsecutivoRecepcion();

        // Crear el radicado con el consecutivo
        Radicado radicado = new Radicado(consecutivo);

        // Establecer las propiedades del radicado si existe
        if (input.propiedadRadicado() != null) {
            PropiedadRadicadoDTO propiedadDTO = input.propiedadRadicado();
            PropiedadesRadicado propiedades = new PropiedadesRadicado(
                    propiedadDTO.page(),
                    propiedadDTO.x(),
                    propiedadDTO.y(),
                    propiedadDTO.height(),
                    propiedadDTO.width(),
                    propiedadDTO.rotationDegrees()
            );

            // Crear un nuevo radicado con las propiedades
            radicado = new Radicado(
                    null,
                    radicado.getEstado(),
                    radicado.getNumeroRadicado(),
                    radicado.getFechaExpedicion(),
                    radicado.getEmisor(),
                    null,
                    radicado.getPrefijo(),
                    radicado.getSufijo(),
                    radicado.getTipo(),
                    propiedades,
                    null, // No hay destinatario para radicados de recepción
                    radicado.getConsecutivo(),
                    null
            );
        }

        // Expedir el radicado con el documento
        radicado.expedir(documentoGuardado);

        consecutivoRepository.save(consecutivo);
        Radicado radicadoGuardado = radicadoRepository.save(radicado);

        return radicadoMapper.toDTO(radicadoGuardado);
    }

    private Documento crearDocumento(RadicarDocumentoRecibidoCommand input) {
        TipoDocumental tipoDocumental = tipoDocumentalRepository
                .findById(UUID.fromString(input.tipoDocumentalId()))
                .orElseThrow(() -> new EntityNotExistsException("Tipo documental no encontrado"));

        UnidadDocumental unidadDocumental = unidadDocumentalRepository
                .findById(UUID.fromString(input.unidadDocumentalId()))
                .orElseThrow(() -> new EntityNotExistsException("Unidad documental no encontrada"));

        Set<Metadato> metadatos = buildMetadatos(input.metadatos());

        List<Anexo> anexos = input.anexos().stream()
                .map(this::buildAnexo)
                .toList();

        return new Documento(
                input.titulo(),
                input.fileId(),
                tipoDocumental,
                unidadDocumental,
                input.autor(),
                metadatos,
                Collections.emptyList(),
                Collections.emptyList(),
                anexos
        );
    }

    private Set<Metadato> buildMetadatos(List<MetadatoDocumentoDTO> metadatosDTO) {
        if (metadatosDTO == null || metadatosDTO.isEmpty()) {
            return Collections.emptySet();
        }

        Map<String, String> mapaMetadatos = metadatosDTO.stream()
                .collect(Collectors.toMap(
                        MetadatoDocumentoDTO::nombre,
                        MetadatoDocumentoDTO::valor
                ));

        var definiciones = definicionMetadatosRepository.buscarPorPatrones(
                new ArrayList<>(mapaMetadatos.keySet())
        );

        return definiciones.stream()
                .map(d -> d.generarMetadato(mapaMetadatos.get(d.getPatron())))
                .collect(Collectors.toSet());
    }

    private Anexo buildAnexo(AnexoDocumentoDTO anexoDTO) {
        return Anexo.builder()
                .nombre(anexoDTO.nombre())
                .descripcion(anexoDTO.descripcion())
                .fileId(anexoDTO.fileId())
                .hash(anexoDTO.hash())
                .bytes(anexoDTO.bytes())
                .extension(anexoDTO.extension())
                .build();
    }

    private Consecutivo obtenerConsecutivoRecepcion() {
        Consecutivo consecutivoEjemplo = new Consecutivo(
                "REC", 
                String.valueOf(Calendar.getInstance().get(Calendar.YEAR)), 
                0, 
                TipoConsecutivo.RECEPCION
        );

        return consecutivoRepository.findByExample(consecutivoEjemplo)
                .orElseGet(() -> {
                    Consecutivo nuevoConsecutivo = new Consecutivo(
                            "REC", 
                            String.valueOf(Calendar.getInstance().get(Calendar.YEAR)), 
                            0, 
                            TipoConsecutivo.RECEPCION
                    );
                    return consecutivoRepository.save(nuevoConsecutivo);
                });
    }
}
