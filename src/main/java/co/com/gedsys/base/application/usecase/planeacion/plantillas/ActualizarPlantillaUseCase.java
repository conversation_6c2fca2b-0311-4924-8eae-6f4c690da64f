package co.com.gedsys.base.application.usecase.planeacion.plantillas;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.plantillas.Plantilla;
import co.com.gedsys.base.domain.plantillas.PlantillaRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ActualizarPlantillaUseCase implements UseCase<ActualizarPlantillaCommand, Void> {
    private final DefinicionMetadatosRepository metadatoRepository;
    private final PlantillaRepository repository;
    private final TipoDocumentalRepository tipoDocumentalRepository;


    @Override
    public Void execute(ActualizarPlantillaCommand input) {
        var plantilla = retrieveTemplate(input);
        updateTitle(plantilla, input.titulo());
        updateTipoDocumental(plantilla, input.tipoDocumentalId());
        setMetadata(plantilla, input.metadatos());
        repository.save(plantilla);
        return null;
    }

    private void setMetadata(Plantilla plantilla, List<String> patterns) {
        if (patterns == null) return;
        var definiciones = metadatoRepository.buscarPorPatrones(patterns);
        plantilla.setEsquemaDeMetadatos(definiciones);
    }

    private Plantilla retrieveTemplate(ActualizarPlantillaCommand input) {
        return repository.findById(input.plantillaId())
                .orElseThrow(() -> new EntityNotExistsException("No se ha encontrado la plantilla con Id " + input.plantillaId()));
    }

    private void updateTipoDocumental(Plantilla plantilla, UUID tipoDocumentalId) {
        Optional.ofNullable(tipoDocumentalId)
                .flatMap(tipoDocumentalRepository::findById)
                .ifPresent(plantilla::setTipoDocumental);
    }

    private void updateTitle(Plantilla plantilla, String title) {
        Optional.ofNullable(title)
                .filter(t -> !t.isEmpty())
                .filter(this::isNotDuplicated)
                .ifPresent(plantilla::setTitulo);
    }

    private boolean isNotDuplicated(String title) {
        boolean alreadyAssigned = repository.checkStock(title);
        if (alreadyAssigned)
            throw new EntityAlreadyExistsException("El título de la plantilla ya se encuentra en uso.");
        return true;
    }

}
