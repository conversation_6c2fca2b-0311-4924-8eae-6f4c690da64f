package co.com.gedsys.base.application.usecase.produccion.documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.mapper.DocumentoApplicationLayerMapper;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.documento.Anexo;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.metadato.Metadato;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CargarDocumentoUseCase implements UseCase<CargarDocumentoCommand, DocumentoDTO> {
    private final DocumentoRepository documentoRepository;
    private final TipoDocumentalRepository tipoDocumentalRepository;
    private final UnidadDocumentalRepository unidadDocumentalRepository;
    private final DefinicionMetadatosRepository definicionMetadatosRepository;
    private final DocumentoApplicationLayerMapper mapper;

    @Transactional
    @Override
    public DocumentoDTO execute(CargarDocumentoCommand input) {
        // Validación de campos obligatorios
        Objects.requireNonNull(input.titulo(), "El título es obligatorio");
        Objects.requireNonNull(input.autor(), "El autor es obligatorio");
        Objects.requireNonNull(input.fileId(), "El fileId es obligatorio");

        var tipoDocumental = tipoDocumentalRepository.findById(input.tipoDocumentalId())
                .orElseThrow(() -> new EntityNotExistsException("Tipo documental no encontrado"));
        var unidadDocumental = unidadDocumentalRepository.findById(input.unidadDocumentalId())
                .orElseThrow(() -> new EntityNotExistsException("Unidad documental no encontrada"));

        // Validar metadatos duplicados
        List<String> nombresMetadatos = input.metadatos().stream().map(CargarDocumentoCommand.Metadato::nombre).toList();
        Set<String> nombresUnicos = new HashSet<>(nombresMetadatos);
        if (nombresUnicos.size() != nombresMetadatos.size()) {
            throw new IllegalArgumentException("No se permiten metadatos duplicados por nombre");
        }

        // Validar metadatos
        Set<DefinicionMetadato> definiciones = definicionMetadatosRepository.buscarPorPatrones(nombresMetadatos);
        Set<String> definidos = definiciones.stream().map(DefinicionMetadato::getPatron).collect(Collectors.toSet());
        List<String> faltantes = nombresMetadatos.stream().filter(n -> !definidos.contains(n)).toList();
        if (!faltantes.isEmpty()) {
            throw new co.com.gedsys.base.application.usecase.produccion.radicacion.exception.MetadatoNoExisteException(
                    "Faltan definiciones de metadatos: " + String.join(", ", faltantes));
        }

        // Validar tipo de metadato
        Map<String, CargarDocumentoCommand.Metadato> metadatosInputMap = input.metadatos().stream()
                .collect(Collectors.toMap(CargarDocumentoCommand.Metadato::nombre, m -> m));
        for (DefinicionMetadato definicion : definiciones) {
            var metadatoInput = metadatosInputMap.get(definicion.getPatron());
            if (metadatoInput != null && definicion.getTipo() != null && !definicion.getTipo().equals(metadatoInput.tipo())) {
                throw new IllegalArgumentException("El tipo del metadato '" + definicion.getPatron() + "' no corresponde con la definición");
            }
        }

        // Construir metadatos de dominio
        Map<String, String> valoresMetadatos = input.metadatos().stream()
                .collect(Collectors.toMap(CargarDocumentoCommand.Metadato::nombre, CargarDocumentoCommand.Metadato::valor));
        Set<Metadato> metadatos = definiciones.stream()
                .map(definicion -> definicion.generarMetadato(valoresMetadatos.get(definicion.getPatron())))
                .collect(Collectors.toSet());

        // Construir anexos
        List<Anexo> anexos = Optional.ofNullable(input.anexos())
                .orElse(List.of())
                .stream()
                .map(a -> new Anexo(null, a.nombre(), a.descripcion(), a.fileId(), a.hash(), a.bytes(), a.extension()))
                .collect(Collectors.toList());

        // Crear documento
        Documento documento = new Documento(
                input.titulo(),
                input.fileId(),
                tipoDocumental,
                unidadDocumental,
                input.autor(),
                metadatos,
                List.of(), // Sin firmas
                List.of(), // Sin aprobaciones
                anexos
        );

        Documento documentoGuardado = documentoRepository.save(documento);
        return mapper.toDTO(documentoGuardado);
    }
}
