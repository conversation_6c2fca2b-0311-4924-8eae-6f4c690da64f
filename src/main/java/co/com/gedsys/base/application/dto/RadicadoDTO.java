package co.com.gedsys.base.application.dto;

public record RadicadoDTO(
        String id,
        Integer numeroRadicado,
        String emisor,
        String fechaExpedicion,
        String observaciones,
        String prefijo,
        String sufijo,
        String tipo,
        ConsecutivoDS consecutivo,
        DocumentoDS documento,
        PropiedadesRadicadoDS propiedadesRadicado,
        ExternalUserDS destinatario
) {
    public record ConsecutivoDS(
            String id,
            Integer contador,
            String prefijo,
            String sufijo,
            String tipo,
            String tipoDocumentalId
    ) {
    }

    public record DocumentoDS(
            String id,
            String titulo,
            String fileId,
            String tipoDocumentalId,
            String clasificacionDocumentalId,
            String unidadDocumentalId
    ) {
    }

    public record PropiedadesRadicadoDS(
        Integer page,
        Integer x,
        Integer y,
        Integer height,
        Integer width,
        Integer rotationDegrees
    ) {
    }

    public record ExternalUserDS(
        String id,
        String name,
        String identificationType,
        String identificationNumber,
        String status
    ) {
    }
}
