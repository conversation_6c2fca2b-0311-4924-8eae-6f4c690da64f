package co.com.gedsys.base.application.usecase.planeacion.seccion;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class AgregarSeccionUseCase implements UseCase<AgregarSeccionCommand, SeccionDTO> {

    private final SeccionRepository repository;
    private final SeccionUseCaseMapper mapper;

    @Override
    public SeccionDTO execute(AgregarSeccionCommand input) {
        var seccion = new Seccion(input.codigo(), input.nombre());
        seccion.setResponsable(input.responsable());

        if (repository.findByCode(seccion.codigo()).isPresent()) {
            throw new EntityAlreadyExistsException(
                    String.format("Unidad productora con el código '%s' ya existe", input.codigo()));
        }

        // Validar si es un nodo principal (sin padre)
        if (input.padreId() == null) {
            if (repository.buscarNodoPrincipalDelOrganigrama().isPresent()) {
                throw new NodoPrincipalOrganigramaException();
            }
        } else {
            var padre = repository.findById(input.padreId())
                    .orElseThrow(() -> new EntityNotExistsException(
                            String.format("Padre de la unidad productora con id '%s' no existe", input.padreId())));
            seccion.setPadre(padre);
        }

        var persistedEntity = repository.save(seccion);
        return mapper.toDto(persistedEntity);
    }
}