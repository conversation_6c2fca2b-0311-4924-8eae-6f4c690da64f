package co.com.gedsys.base.application.usecase.planeacion.plantillas;

import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.UUID;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Accessors(fluent = true)
@Builder
public class ActualizarPlantillaCommand {
    private UUID plantillaId;
    private String titulo;
    private UUID tipoDocumentalId;
    private List<String> metadatos;
}

