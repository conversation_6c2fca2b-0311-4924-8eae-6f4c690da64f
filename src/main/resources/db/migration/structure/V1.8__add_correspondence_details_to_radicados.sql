-- Agregar campos para correspondencia en radicados
-- Issue #8: Ag<PERSON>gar remitente, destino y destinatario al servicio de recepción

-- Agregar columnas para la dualidad de roles en correspondencia
ALTER TABLE radicados 
ADD COLUMN remitente_id UUID,
ADD COLUMN destino_id UUID,
ADD COLUMN destinatario_interno_id UUID;

-- Agregar foreign keys
ALTER TABLE radicados
ADD CONSTRAINT fk_radicados_remitente 
    FOREIGN KEY (remitente_id) REFERENCES external_users(id),
ADD CONSTRAINT fk_radicados_destino 
    FOREIGN KEY (destino_id) REFERENCES secciones(id),
ADD CONSTRAINT fk_radicados_destinatario_interno 
    FOREIGN KEY (destinatario_interno_id) REFERENCES usuarios_secciones(id);

-- Agregar índices para optimizar consultas
CREATE INDEX idx_radicados_remitente_id ON radicados(remitente_id);
CREATE INDEX idx_radicados_destino_id ON radicados(destino_id);
CREATE INDEX idx_radicados_destinatario_interno_id ON radicados(destinatario_interno_id);

-- Comentarios para documentación
COMMENT ON COLUMN radicados.remitente_id IS 'Usuario externo que inicia el trámite (para recepción)';
COMMENT ON COLUMN radicados.destino_id IS 'Oficina o sección a la cual se dirige el documento';
COMMENT ON COLUMN radicados.destinatario_interno_id IS 'Usuario interno objeto del trámite (opcional)';
